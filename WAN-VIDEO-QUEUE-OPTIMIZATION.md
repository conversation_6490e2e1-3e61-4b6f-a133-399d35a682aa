# 🚀 WAN视频生成长队列优化

## 🎯 问题分析

用户反馈：**"又失败了。主要是排队时间比较长吧"**

从日志分析发现：
- ✅ 网络连接已稳定（没有SocketError）
- ✅ 轮询机制正常工作
- ❌ **队列位置116→100，需要很长时间才能轮到**
- ❌ **40次轮询限制不够长队列使用**

## 🔧 全面优化方案

### 1. 📈 智能动态轮询策略

**动态调整轮询次数**：
```typescript
// 基础60次，根据队列长度动态扩展
let maxPolls = 60
if (currentQueuePosition > 80) {
  maxPolls = Math.max(maxPolls, 120) // 队列很长：120次 (20-30分钟)
} else if (currentQueuePosition > 30) {
  maxPolls = Math.max(maxPolls, 90)  // 队列中等：90次 (15-20分钟)
}
```

**智能等待时间**：
```typescript
// 根据队列位置调整等待间隔
if (position > 50) {
  waitTime = 15-25秒 // 队列很长，减少请求频率
} else if (position > 20) {
  waitTime = 10-18秒 // 队列中等
} else {
  waitTime = 6-12秒  // 快到了，加快检查
}
```

### 2. 🕒 实时队列信息显示

**UI实时更新**：
- 显示当前队列位置：`排队中 #116`
- 预计等待时间：`约58分钟`
- 队列进度跟踪：`116→110→103→100...`

**按钮状态优化**：
```tsx
{generatingVideos.has(shot.id) ? (
  <div className="text-center">
    <Loader2 className="animate-spin" />
    {videoQueueInfo.has(shot.id) ? (
      <div className="text-xs">
        <div>排队中 #{position}</div>
        <div>预计 {estimated}分钟</div>
      </div>
    ) : (
      "生成视频中..."
    )}
  </div>
) : "🎬 立即生成视频"}
```

### 3. 📊 智能时间估算

**基于队列位置的时间预测**：
```typescript
// 假设每个任务平均30秒
const estimatedMinutes = Math.round(currentQueuePosition * 0.5)
const estimated = estimatedMinutes > 60 
  ? `${Math.round(estimatedMinutes/60)}小时` 
  : `${estimatedMinutes}分钟`
```

### 4. 🎯 用户友好的错误提示

**分层错误信息**：
```typescript
if (queuePos > 50) {
  throw new Error(`队列太长（位置${queuePos}），预计等待时间过长。建议稍后重试或选择非高峰时段。`)
} else {
  throw new Error(`视频生成超时（已等待${Math.round(pollCount * 12 / 60)}分钟），请稍后重试。`)
}
```

## 📋 优化效果对比

### 优化前：
- ❌ 固定40次轮询（6-10分钟超时）
- ❌ 固定6-15秒等待间隔
- ❌ 无队列信息显示
- ❌ 超时就直接失败
- ❌ 不适合长队列场景

### 优化后：
- ✅ 动态60-120次轮询（10-30分钟）
- ✅ 智能6-25秒变速等待
- ✅ 实时队列位置显示
- ✅ 预计等待时间提示
- ✅ 用户体验大幅提升

## 🚀 应对不同场景

### 🟢 短队列（位置1-20）
- **轮询次数**：60次
- **等待间隔**：6-12秒
- **预计时间**：1-10分钟
- **用户体验**：快速完成

### 🟡 中等队列（位置21-50）
- **轮询次数**：90次
- **等待间隔**：10-18秒
- **预计时间**：10-25分钟
- **用户体验**：实时进度显示

### 🟠 长队列（位置51-100）
- **轮询次数**：120次
- **等待间隔**：15-25秒
- **预计时间**：25-50分钟
- **用户体验**：明确时间预期

### 🔴 超长队列（位置100+）
- **智能处理**：提示用户等待时间过长
- **建议操作**：选择非高峰时段重试
- **避免浪费**：不让用户无意义等待

## 🎯 用户操作建议

### 最佳使用时机：
- ✅ **非高峰时段**（队列位置通常<30）
- ✅ **早上或深夜**（服务器负载较低）
- ✅ **单个视频生成**（避免批量加重负载）

### 遇到长队列时：
- 🕒 **耐心等待**：系统会显示预计时间
- ⏰ **稍后重试**：选择队列较短的时间
- 🔄 **分批处理**：不要同时生成多个视频

## 📝 技术实现要点

1. **状态管理**：增加`videoQueueInfo`状态跟踪队列信息
2. **UI更新**：实时显示队列位置和预计时间
3. **动态策略**：根据队列长度调整轮询参数
4. **用户体验**：提供明确的等待时间预期
5. **错误处理**：针对不同情况给出具体建议

现在WAN视频生成功能已经能够**智能处理各种队列长度**，给用户**清晰的等待预期**！🎬✨