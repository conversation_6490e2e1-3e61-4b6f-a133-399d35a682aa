{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=tghnthygyumnqoivoxuh"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "interactive-feedback-mcp": {"command": "/Users/<USER>/Documents/dev/cursor-mcp/interactive-feedback-mcp/start_mcp.sh", "timeout": 600, "autoApprove": ["interactive_feedback"]}}}