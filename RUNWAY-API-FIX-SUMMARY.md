# Runway API 修复总结

## 问题诊断

### 原始问题
1. **API URL 不匹配**：代码中使用硬编码的URL `https://api.aimlapi.com/chat/completions`，但环境变量中配置的是 `https://api.apicore.ai/runwayml/v1/image_to_video`
2. **API密钥余额不足**：当前Runway API密钥余额不足，无法生成视频
3. **缺少备选方案**：当Runway API不可用时，没有其他视频生成API作为备选
4. **API端点更新**：用户更换了新的Runway API端点和参数格式

## 修复方案

### 1. 更新API配置
- **新API端点**：`https://api.apicore.ai/runway/pro/generate`
- **新参数格式**：适配新的API请求体结构
- **修改文件**：`app/api/runway/video/route.ts` 和 `app/api/runway/status/route.ts`

### 2. 新的API参数格式
```javascript
{
  callback_url: "", // 可选回调URL
  ratio: "16:9", // 视频比例
  prompt: "视频提示词", // 必需
  style: "cinematic", // 视频风格
  model: "gen3", // 模型名称
  options: {
    seconds: 10, // 视频时长
    motion_vector: {
      x: 0,
      y: 0.4,
      z: 0,
      r: -6,
      bg_x_pan: 0,
      bg_y_pan: 0
    }
  }
}
```

### 3. 添加备选API方案
- **主要API**：Runway API (`https://api.apicore.ai/runway/pro/generate`)
- **备选API**：WAN API (`https://queue.fal.run/fal-ai/wan/v2.2-a14b/image-to-video/turbo`)
- **自动切换逻辑**：
  - 首先尝试Runway API
  - 如果Runway API余额不足或失败，自动切换到WAN API
  - 支持两种API的不同参数格式

### 4. 改进的错误处理
- **余额检测**：自动检测API余额不足的情况
- **优雅降级**：当主要API不可用时自动使用备选API
- **详细日志**：记录API切换过程和错误信息
- **标准化响应**：统一API响应格式

## 配置要求

### 环境变量
```bash
# Runway API配置
RUNWAY_API_KEY=your_new_api_key
RUNWAY_API_URL=https://api.apicore.ai/runway/pro/generate

# WAN API配置（备选）
wan_api_key=de1acad1-06d5-4e7d-8dc6-0a6fae01f7a6:04d00ce5843d3994bcab12430391bc5d
```

## 使用说明

### 视频生成流程
1. **优先使用Runway API**：系统首先尝试使用Runway API生成视频
2. **自动切换**：如果Runway API余额不足或失败，自动切换到WAN API
3. **统一接口**：前端无需修改，使用相同的API接口

### API参数
```javascript
{
  promptText: "视频提示词", // 必需
  model: "gen3", // 可选，默认gen3
  duration: 10, // 可选，默认10秒
  ratio: "16:9", // 可选，默认16:9
  style: "cinematic", // 可选，默认cinematic
  motion_vector: { // 可选，运动向量
    x: 0,
    y: 0.4,
    z: 0,
    r: -6,
    bg_x_pan: 0,
    bg_y_pan: 0
  }
}
```

## 测试验证

### 1. 测试Runway API
```bash
curl -X POST "https://api.apicore.ai/runway/pro/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "callback_url": "",
    "ratio": "16:9",
    "prompt": "一只小猫",
    "style": "cinematic",
    "model": "gen3",
    "options": {
      "seconds": 10,
      "motion_vector": {
        "x": 0,
        "y": 0.4,
        "z": 0,
        "r": -6,
        "bg_x_pan": 0,
        "bg_y_pan": 0
      }
    }
  }'
```

### 2. 测试WAN API
```bash
curl -X POST "https://queue.fal.run/fal-ai/wan/v2.2-a14b/image-to-video/turbo" \
  -H "Content-Type: application/json" \
  -H "Authorization: Key YOUR_API_KEY" \
  -d '{"image_url":"https://example.com/test.jpg","prompt":"test"}'
```

## 状态检查

### 当前状态
- **视频生成**：✅ 正常工作
- **状态检查**：⚠️ 端点可能不可用，返回模拟响应
- **备选API**：✅ WAN API作为备选正常工作

### 状态检查说明
由于新的Runway API可能没有提供状态检查端点，我们暂时返回模拟响应。如果需要实时状态检查，建议：
1. 联系API提供商获取正确的状态检查端点
2. 使用轮询机制定期检查任务状态
3. 实现回调机制接收完成通知

## 下一步建议

### 1. 完善状态检查
- 联系API提供商获取正确的状态检查端点
- 实现回调URL机制
- 添加任务状态轮询

### 2. 监控API使用情况
- 添加API使用量监控
- 设置余额告警机制
- 记录API调用日志

### 3. 优化备选方案
- 考虑添加更多视频生成API作为备选
- 实现API性能对比和自动选择
- 优化错误提示信息

## 修复状态

✅ **已完成**：
- 更新API端点和参数格式
- 修复API URL配置问题
- 添加WAN API作为备选方案
- 实现自动切换逻辑
- 改进错误处理和日志记录
- 标准化API响应格式

⚠️ **需要注意**：
- 状态检查端点需要进一步确认
- 建议监控API使用量和余额

🔄 **待优化**：
- 完善状态检查机制
- 添加更多备选API
- 实现API性能监控 