# ScriptVivid AI - 快速修复指南

## 问题诊断

页面显示"No Projects Found"错误的可能原因：

### 1. 用户认证问题
- 用户可能没有正确登录
- 用户ID格式错误

### 2. 数据库连接问题  
- Supabase连接配置问题
- 网络连接问题

### 3. 演示模式逻辑问题
- 演示数据没有正确加载

## 快速解决步骤

### 方案1：清除浏览器缓存和重新登录
1. 打开浏览器开发者工具 (F12)
2. 在Console标签中运行：`localStorage.clear()`
3. 刷新页面
4. 重新登录

### 方案2：手动触发演示模式
1. 在Console中运行以下代码：
```javascript
// 创建演示用户
const demoUser = {
  id: "550e8400-e29b-41d4-a716-446655440000",
  email: "<EMAIL>", 
  user_metadata: { name: "Demo User" },
  app_metadata: {},
  aud: "authenticated",
  created_at: new Date().toISOString()
};
localStorage.setItem("demo-user", JSON.stringify(demoUser));

// 创建演示项目
const demoProjects = [
  {
    id: "550e8400-e29b-41d4-a716-446655440001",
    user_id: "550e8400-e29b-41d4-a716-446655440000",
    title: "测试项目",
    description: "这是一个测试项目",
    status: "in_progress",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];
localStorage.setItem("demo-projects", JSON.stringify(demoProjects));

// 刷新页面
window.location.reload();
```

### 方案3：检查环境变量
确认 .env.local 文件中的配置是否正确：
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- RUNWAY_API_KEY

## 新增的RUNWAY视频API测试

RUNWAY API配置看起来正常：
- URL: https://api.apicore.ai/runwayml/v1/image_to_video
- KEY: 已配置

API路由位置：`app/api/runway/video/route.ts`