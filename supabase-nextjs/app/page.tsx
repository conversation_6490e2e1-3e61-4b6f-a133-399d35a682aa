'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function Home() {
  const [status, setStatus] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setStatus('正在测试连接...')
    
    try {
      // 测试基本连接
      const { error } = await supabase.from('test').select('*').limit(1)
      
      if (error) {
        if (error.code === 'PGRST116') {
          setStatus('✅ Supabase连接成功！但test表不存在（这是正常的）')
        } else {
          setStatus(`❌ 连接错误: ${error.message}`)
        }
      } else {
        setStatus('✅ Supabase连接成功！')
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      setStatus(`❌ 连接失败: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Supabase 连接测试
          </h1>
          
          <div className="space-y-4">
            <button
              onClick={testConnection}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试连接'}
            </button>
            
            {status && (
              <div className="p-4 rounded-md bg-gray-50">
                <p className="text-sm text-gray-700">{status}</p>
              </div>
            )}
            
            <div className="text-xs text-gray-500">
              <p>请确保在 .env.local 文件中配置了正确的 Supabase 凭据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 