# SORA API 配置指南

## 1. 获取API密钥

1. 访问 [APICore.ai](https://apicore.ai)
2. 注册账户并登录
3. 在控制台中获取你的API密钥

## 2. 配置环境变量

在你的 `.env.local` 文件中添加以下配置：

```bash
# SORA API 配置
SORA_API_KEY=sk-your-api-key-here
SORA_API_URL=https://api.apicore.ai/v1/images/generations
```

## 3. API 参数说明

### 请求参数
- `prompt`: 图片生成提示词
- `model`: 模型名称，默认为 "gpt-image-1"
- `size`: 图片尺寸，支持 "1024x1024", "1792x1024", "1024x1792"
- `n`: 生成图片数量，默认为 1

### 响应格式
```json
{
  "success": true,
  "image_url": "https://example.com/image.jpg",
  "api": "sora",
  "message": "SORA图片生成成功"
}
```

## 4. 使用说明

1. 在AI分析界面中选择 "SORA" 作为图片生成模型
2. 输入图片提示词
3. 点击生成图片按钮
4. 等待图片生成完成

## 5. 注意事项

- SORA API 需要有效的API密钥
- 图片生成可能需要一些时间
- 确保网络连接稳定
- 如果遇到错误，请检查API密钥是否正确配置

## 6. 故障排除

### 常见错误
1. **API密钥未配置**: 确保在 `.env.local` 中正确设置了 `SORA_API_KEY`
2. **网络错误**: 检查网络连接和防火墙设置
3. **配额限制**: 检查API使用配额是否已用完

### 调试信息
在浏览器控制台中可以看到详细的API请求和响应日志。 