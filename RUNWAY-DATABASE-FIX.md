# 🔧 RUNWAY数据库修复指南

## 🐛 问题描述

当前RUNWAY视频生成功能遇到数据库错误：
```
Could not find the 'runway_task_id' column of 'shots' in the schema cache
```

这是因为数据库表结构缺少`runway_task_id`列。

## 🛠️ 解决方案

### 方法1：通过Supabase Dashboard（推荐）

1. **登录Supabase Dashboard**
   - 访问：https://supabase.com/dashboard
   - 选择你的项目

2. **打开SQL编辑器**
   - 点击左侧菜单的"SQL Editor"
   - 点击"New query"

3. **执行SQL命令**
   ```sql
   -- 添加runway_task_id列到shots表
   ALTER TABLE shots ADD COLUMN IF NOT EXISTS runway_task_id TEXT;
   
   -- 验证列已添加
   SELECT column_name, data_type, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'shots' 
   AND column_name = 'runway_task_id';
   ```

4. **点击"Run"执行**

### 方法2：通过API端点

1. **确保应用正在运行**
   ```bash
   npm run dev
   ```

2. **调用迁移API**
   ```bash
   curl -X POST "http://localhost:3000/api/db-migrate" \
     -H "Content-Type: application/json"
   ```

### 方法3：通过psql命令行

```bash
psql "postgresql://postgres.tghnthygyumngoivoxuh.supabase.co:5432/postgres?user=postgres.tghnthygyumngoivoxuh&password=ScriptVivid2024!" -f scripts/add-runway-column.sql
```

## ✅ 验证修复

执行以下SQL查询验证列已添加：
```sql
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'shots' 
AND column_name = 'runway_task_id';
```

应该返回：
```
column_name     | data_type | is_nullable
----------------|-----------|------------
runway_task_id  | text      | YES
```

## 🎯 功能说明

添加`runway_task_id`列后，RUNWAY视频生成功能将：

1. **保存任务ID**：将RUNWAY API返回的task_id保存到数据库
2. **显示下载界面**：不显示示例视频，而是显示下载按钮
3. **支持手动下载**：用户可以通过task_id下载真实生成的视频

## 🔄 临时解决方案

在数据库表结构更新完成前，代码会自动过滤`runway_task_id`字段，避免数据库错误。但RUNWAY任务ID不会被保存。

## 📝 注意事项

- 这个修改不会影响现有数据
- WAN API功能不受影响
- 修改后需要重启应用以确保缓存更新 