# WAN 图生视频 API 测试指南

## 🎬 完成的功能

### ✅ API 路由
- **视频生成**: `/api/wan/video` (POST)
- **状态检查**: `/api/wan/status` (GET/POST)

### ✅ 前端功能
- **单个分镜视频生成**: 每个分镜卡片都有"Generate Video"按钮
- **批量视频生成**: 顶部"Generate All Videos"按钮
- **视频预览**: 生成完成后直接在界面预览
- **轮询机制**: 自动等待视频生成完成

### ✅ 数据库支持
- `video_url` 字段已在 Shot 接口中定义
- 支持演示模式和真实数据库模式

## 🧪 测试步骤

### 1. 准备测试环境
```bash
# 确保开发服务器运行在正确端口
npm run dev
# 服务器应该运行在 http://localhost:3000 或 3001
```

### 2. 环境变量检查
确认 `.env.local` 中的配置：
```
wan_api_key=de1acad1-06d5-4e7d-8dc6-0a6fae01f7a6:04d00ce5843d3994bcab12430391bc5d
wan_api_url=https://queue.fal.run/fal-ai/wan/v2.2-a14b/image-to-video/turbo
```

### 3. 功能测试
1. **访问 storyboards 页面**: http://localhost:3000/storyboards
2. **生成图片**: 先为分镜生成图片
3. **生成视频**: 点击紫色的"Generate Video"按钮
4. **观察状态**: 按钮会显示"Generating..."，轮询过程会在控制台显示
5. **预览视频**: 生成完成后会在右侧显示视频播放器

### 4. 控制台日志检查
生成视频时应该看到：
```
🎬 开始生成视频，分镜ID: xxx
🖼️ 图片URL: xxx
📝 视频提示词: xxx
✅ 视频生成请求成功: {request_id: "xxx"}
🔄 轮询状态检查 1/60
📊 状态检查结果: IN_QUEUE/IN_PROGRESS/COMPLETED
🎯 视频生成完成: {video_url: "xxx"}
💾 视频URL已保存到数据库
```

### 5. 错误处理测试
- **无图片**: 尝试在没有图片的情况下生成视频
- **无视频提示词**: 尝试在视频提示词为空时生成视频
- **API 超时**: 观察长时间生成的处理

## 🔧 API 格式说明

### 请求格式
```json
{
  "image_url": "https://example.com/image.jpg",
  "prompt": "The character moves slowly..."
}
```

### 响应格式
```json
{
  "status": "IN_QUEUE",
  "request_id": "xxx-xxx-xxx",
  "response_url": "https://queue.fal.run/...",
  "status_url": "https://queue.fal.run/.../status"
}
```

### 最终结果格式
```json
{
  "video_url": "https://v3.fal.media/files/xxx.mp4",
  "seed": 12345
}
```

## 🎯 预期结果
- 视频生成通常需要 30 秒到 3 分钟
- 生成的视频会自动保存到数据库
- 界面会实时更新显示生成状态
- 完成后可直接在浏览器中预览视频