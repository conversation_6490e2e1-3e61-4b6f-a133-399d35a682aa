"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { createClient } from "@/lib/supabase"
import type { User } from "@supabase/supabase-js"

interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name?: string) => Promise<void>
  signOut: () => Promise<void>
  signInDemo: () => Promise<void>
  isDemoMode: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Use the same ID as in the database sample data
const DEMO_USER_ID = "550e8400-e29b-41d4-a716-446655440000"

// Create a mock user that matches the database sample data
const createMockUser = (email: string, name: string): User => ({
  id: DEMO_USER_ID, // Use the same ID as sample data
  email,
  user_metadata: { name, full_name: name },
  app_metadata: {},
  aud: "authenticated",
  created_at: new Date().toISOString(),
  role: "authenticated",
  updated_at: new Date().toISOString(),
  email_confirmed_at: new Date().toISOString(),
  phone_confirmed_at: undefined,
  confirmation_sent_at: undefined,
  confirmed_at: new Date().toISOString(),
  last_sign_in_at: new Date().toISOString(),
  recovery_sent_at: undefined,
  new_email: undefined,
  new_phone: undefined,
  phone: undefined,
  is_anonymous: false,
  factors: [],
  identities: [],
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isDemoMode, setIsDemoMode] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    // Check for demo mode first
    const demoUser = localStorage.getItem("demo-user")
    if (demoUser) {
      try {
        const parsedUser = JSON.parse(demoUser)
        setUser(parsedUser)
        setIsDemoMode(true)
        setLoading(false)
        return
      } catch (error) {
        localStorage.removeItem("demo-user")
      }
    }

    // Get initial session
    const getSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        setUser(session?.user ?? null)
        setIsDemoMode(false)
      } catch (error) {
        console.error("Error getting session:", error)
      } finally {
        setLoading(false)
      }
    }

    getSession()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null)
      setIsDemoMode(false)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signInDemo = async () => {
    console.log("Demo sign in initiated")

    // Create a demo user with the same ID as sample data
    const demoUser = createMockUser("<EMAIL>", "Demo User")
    localStorage.setItem("demo-user", JSON.stringify(demoUser))
    setUser(demoUser)
    setIsDemoMode(true)
    setLoading(false)

    console.log("Demo user created with ID:", demoUser.id)
    console.log("Demo user state set:", demoUser)
  }

  const signIn = async (email: string, password: string) => {
    console.log("AuthContext signIn called with:", email)

    // Check if this is a demo login attempt
    if (email === "<EMAIL>") {
      await signInDemo()
      setLoading(false)
      return
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      console.log("Supabase signIn result:", { data, error })

      if (error) {
        // Handle specific error cases
        if (error.message.includes("Email not confirmed")) {
          throw new Error("Please verify your email address before signing in.")
        } else if (error.message.includes("Invalid login credentials")) {
          throw new Error("Invalid email or password. Please check your credentials and try again.")
        } else {
          throw error
        }
      }
    } catch (error) {
      console.error("Sign in error:", error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, name?: string) => {
    console.log("AuthContext signUp called with:", email, name)

    try {
      // For demo purposes, if it's a demo email, just create a local user
      if (email === "<EMAIL>") {
        await signInDemo()
        return
      }

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
            full_name: name,
          },
        },
      })

      console.log("Supabase signUp result:", { data, error })

      if (error) {
        if (error.message.includes("User already registered")) {
          throw new Error("An account with this email already exists. Please sign in instead.")
        } else {
          throw error
        }
      }

      // If signup successful, the user might need to confirm email
      if (data.user && !data.session) {
        throw new Error("Please check your email and click the confirmation link to complete registration.")
      }
    } catch (error) {
      console.error("Sign up error:", error)
      throw error
    }
  }

  const signOut = async () => {
    // Clear demo user
    localStorage.removeItem("demo-user")
    setIsDemoMode(false)

    // Sign out from Supabase
    const { error } = await supabase.auth.signOut()
    if (error) throw error

    setUser(null)
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    signInDemo,
    isDemoMode,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
