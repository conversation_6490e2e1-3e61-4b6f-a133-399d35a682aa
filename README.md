# Supabase Next.js 项目

这是一个配置了 Supabase 的 Next.js 项目，用于测试和开发。

## 快速开始

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**
   
   创建 `.env.local` 文件并添加以下内容：
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **测试连接**
   
   打开 http://localhost:3000 并点击"测试连接"按钮

## Supabase 设置步骤

### 1. 创建 Supabase 项目

1. 访问 [supabase.com](https://supabase.com)
2. 点击 "Start your project"
3. 选择你的 GitHub 账户
4. 创建新项目
5. 等待项目初始化完成

### 2. 获取项目凭据

1. 在 Supabase 控制台中，进入 Settings > API
2. 复制以下信息：
   - Project URL
   - anon/public key

### 3. 配置环境变量

将复制的信息粘贴到 `.env.local` 文件中：
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 4. 创建数据库表（可选）

在 Supabase 控制台的 SQL Editor 中运行以下 SQL：

```sql
-- 创建用户表
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建测试表
CREATE TABLE test (
  id SERIAL PRIMARY KEY,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入测试数据
INSERT INTO test (message) VALUES ('Hello Supabase!');
```

## 项目结构

```
supabase-nextjs/
├── app/                 # Next.js App Router
│   └── page.tsx        # 主页面（连接测试）
├── lib/
│   └── supabase.ts     # Supabase 客户端配置
├── .env.local          # 环境变量（需要手动创建）
└── README.md           # 项目说明
```

## 故障排除

### 连接错误
- 确保 `.env.local` 文件存在且包含正确的凭据
- 检查 Supabase 项目是否正常运行
- 确认网络连接正常

### 环境变量未加载
- 重启开发服务器：`npm run dev`
- 检查文件名是否为 `.env.local`（不是 `.env.local.txt`）

## 下一步

配置成功后，你可以：
1. 添加更多数据库表
2. 实现用户认证
3. 创建数据操作功能
4. 部署到生产环境
