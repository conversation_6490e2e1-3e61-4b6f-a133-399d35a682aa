
> my-v0-project@0.1.0 dev
> next dev

 ⚠ Port 3000 is in use, trying 3001 instead.
   ▲ Next.js 15.2.4
   - Local:        http://localhost:3001
   - Network:      http://*************:3001
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 2.3s
 ✓ Compiled /middleware in 167ms (101 modules)
 ○ Compiling / ...
 ✓ Compiled / in 2.8s (1122 modules)
 HEAD / 200 in 3319ms
 ✓ Compiled in 688ms (507 modules)
 ○ Compiling /storyboards ...
 ✓ Compiled /storyboards in 664ms (1161 modules)
 HEAD /storyboards 200 in 971ms
 GET /storyboards 200 in 92ms
 ✓ Compiled /video-download in 454ms (1176 modules)
 GET /video-download 200 in 530ms
 GET /storyboards 200 in 29ms
 ✓ Compiled /_not-found in 301ms (1180 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 529ms
 ○ Compiling /api/schnell ...
 ✓ Compiled /api/schnell in 572ms (1199 modules)
🚀 Schnell API 请求开始
📝 提示词: 一位欧美女性站着弯下腰，双手撑地，她身穿一件粉色运动背心和深灰色瑜伽裤。一只哈士奇站在她的后背上，另一只哈士奇站在她后面（女性头向着镜头，挡住了站在她后面的哈士奇）。明亮的客厅背景，有窗户和绿植。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "一位欧美女性站着弯下腰，双手撑地，她身穿一件粉色运动背心和深灰色瑜伽裤。一只哈士奇站在她的后背上，另一只哈士奇站在她后面（女性头向着镜头，挡住了站在她后面的哈士奇）。明亮的客厅背景，有窗户和绿植。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/tiger/T-T8Ni3ItoWS1d-F1LH6o.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10807079216465354
  },
  "seed": 22554388,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "一位欧美女性站着弯下腰，双手撑地，她身穿一件粉色运动背心和深灰色瑜伽裤。一只哈士奇站在她的后背上，另一只哈士奇站在她后面（女性头向着镜头，挡住了站在她后面的哈士奇）。明亮的客厅背景，有窗户和绿植。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '530',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 13:04:42 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '6872e29c-a573-4c97-971c-ffc212fe4cbd',
  'x-fal-served-from': 'ae6a2cc5-400a-4739-af90-dd0286c21a46',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10807079216465354
🖼️ 生成的图片URL: https://v3.fal.media/files/tiger/T-T8Ni3ItoWS1d-F1LH6o.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 2133ms
 ○ Compiling /api/store-image ...
 ✓ Compiled /api/store-image in 582ms (1202 modules)
存储图片: https://v3.fal.media/files/tiger/T-T8Ni3ItoWS1d-F1LH6o.jpeg 分镜ID: 0ddc46e3-39a7-47eb-b86d-bccdedf17d1f
图片已保存到本地: /images/shot-0ddc46e3-39a7-47eb-b86d-bccdedf17d1f-1753967085214.jpg
 POST /api/store-image 200 in 2620ms
 ✓ Compiled in 572ms (542 modules)
 GET /storyboards 200 in 80ms
 ✓ Compiled in 1918ms (1177 modules)
 GET /storyboards 200 in 339ms
 ✓ Compiled in 4.1s (1177 modules)
 GET /storyboards 200 in 102ms
 ✓ Compiled in 802ms (1177 modules)
 GET /storyboards 200 in 90ms
 ✓ Compiled /api/schnell in 176ms (656 modules)
🚀 Schnell API 请求开始
📝 提示词: [电影感镜头，柔和的晨光，印度乡村一间破旧的土坯房门口。安嘉莉（Anjali），38岁，印度妇女，面容忧虑，穿着褪色的蓝色棉布纱丽，正在为儿子拉维（Ravi）整理领口。拉维，9岁，印度男孩，穿着整洁的白色衬衫和卡其色短裤校服，背着棕色书包。拉詹（Rajan），40岁，印度男人，身材瘦削，黑色胡须，面容严肃，穿着磨损的深棕色长袖衬衫和深色长裤，站在门内。普莉娅（Priya），8岁，印度女孩，扎马尾辫，眼神渴望，穿着破旧的黄色连衣裙式上衣和配套的旧裤子，站在一旁。背景是简陋的土坯房，细节丰富，电影质感，4K画质。]
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "[电影感镜头，柔和的晨光，印度乡村一间破旧的土坯房门口。安嘉莉（Anjali），38岁，印度妇女，面容忧虑，穿着褪色的蓝色棉布纱丽，正在为儿子拉维（Ravi）整理领口。拉维，9岁，印度男孩，穿着整洁的白色衬衫和卡其色短裤校服，背着棕色书包。拉詹（Rajan），40岁，印度男人，身材瘦削，黑色胡须，面容严肃，穿着磨损的深棕色长袖衬衫和深色长裤，站在门内。普莉娅（Priya），8岁，印度女孩，扎马尾辫，眼神渴望，穿着破旧的黄色连衣裙式上衣和配套的旧裤子，站在一旁。背景是简陋的土坯房，细节丰富，电影质感，4K画质。]",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/kangaroo/LqYjVtRVUlOEz2Fr7hE1_.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11863258201628923
  },
  "seed": 1158837116,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "[电影感镜头，柔和的晨光，印度乡村一间破旧的土坯房门口。安嘉莉（Anjali），38岁，印度妇女，面容忧虑，穿着褪色的蓝色棉布纱丽，正在为儿子拉维（Ravi）整理领口。拉维，9岁，印度男孩，穿着整洁的白色衬衫和卡其色短裤校服，背着棕色书包。拉詹（Rajan），40岁，印度男人，身材瘦削，黑色胡须，面容严肃，穿着磨损的深棕色长袖衬衫和深色长裤，站在门内。普莉娅（Priya），8岁，印度女孩，扎马尾辫，眼神渴望，穿着破旧的黄色连衣裙式上衣和配套的旧裤子，站在一旁。背景是简陋的土坯房，细节丰富，电影质感，4K画质。]"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '955',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 13:08:00 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '680f7ef5-3bc2-4cfc-bb6d-1ecccce281dc',
  'x-fal-served-from': '542bc753-fc43-4a47-87e8-e36d29ebaebc',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11863258201628923
🖼️ 生成的图片URL: https://v3.fal.media/files/kangaroo/LqYjVtRVUlOEz2Fr7hE1_.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1360ms
 ✓ Compiled /api/store-image in 189ms (659 modules)
存储图片: https://v3.fal.media/files/kangaroo/LqYjVtRVUlOEz2Fr7hE1_.jpeg 分镜ID: e9d6e64d-012f-4e7a-9597-17ab25b7173f
图片已保存到本地: /images/shot-e9d6e64d-012f-4e7a-9597-17ab25b7173f-1753967282424.jpg
 POST /api/store-image 200 in 2152ms
 ✓ Compiled in 175ms (539 modules)
 GET /storyboards 200 in 65ms
🚀 Schnell API 请求开始
📝 提示词: [特写镜头，光线昏暗，印度乡村破旧的土坯房内。普莉娅（Priya），8岁，印度女孩，眼神渴望，穿着破旧的黄色连衣裙式上衣，怯生生地看着父亲拉詹。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫。普莉娅头顶浮现一个思想泡泡，里面是一本打开的书。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "[特写镜头，光线昏暗，印度乡村破旧的土坯房内。普莉娅（Priya），8岁，印度女孩，眼神渴望，穿着破旧的黄色连衣裙式上衣，怯生生地看着父亲拉詹。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫。普莉娅头顶浮现一个思想泡泡，里面是一本打开的书。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/tiger/8hejcMZveqfZdcLu0YTzn.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11819297354668379
  },
  "seed": 314635375,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "[特写镜头，光线昏暗，印度乡村破旧的土坯房内。普莉娅（Priya），8岁，印度女孩，眼神渴望，穿着破旧的黄色连衣裙式上衣，怯生生地看着父亲拉詹。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫。普莉娅头顶浮现一个思想泡泡，里面是一本打开的书。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '686',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 13:08:10 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '96b67c15-3c45-45dc-bf62-bf532dcceb80',
  'x-fal-served-from': 'd251f751-1676-4eed-b58c-30ebbd71a43d',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11819297354668379
🖼️ 生成的图片URL: https://v3.fal.media/files/tiger/8hejcMZveqfZdcLu0YTzn.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1084ms
存储图片: https://v3.fal.media/files/tiger/8hejcMZveqfZdcLu0YTzn.jpeg 分镜ID: 0b84477a-ab03-4b40-b7ef-8ba1b89b0a7e
图片已保存到本地: /images/shot-0b84477a-ab03-4b40-b7ef-8ba1b89b0a7e-1753967292704.jpg
 POST /api/store-image 200 in 1981ms
 ✓ Compiled in 220ms (539 modules)
 GET /storyboards 200 in 26ms
🚀 Schnell API 请求开始
📝 提示词: [中景镜头，光线昏暗，印度乡村破旧的土坯房内。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫，看到女儿普莉娅的想法，脸上露出不悦。他头顶浮现出洗碗的图标，他严厉地指着普莉娅（Priya），8岁，印度女孩，眼神从渴望变为害怕，穿着破旧的黄色连衣裙式上衣，命令她去做家务。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "[中景镜头，光线昏暗，印度乡村破旧的土坯房内。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫，看到女儿普莉娅的想法，脸上露出不悦。他头顶浮现出洗碗的图标，他严厉地指着普莉娅（Priya），8岁，印度女孩，眼神从渴望变为害怕，穿着破旧的黄色连衣裙式上衣，命令她去做家务。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/lion/POOO16IFNmObhWDRH3WgK.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10913584008812904
  },
  "seed": 911985279,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "[中景镜头，光线昏暗，印度乡村破旧的土坯房内。拉詹（Rajan），40岁，印度男人，面容严肃，穿着深棕色衬衫，看到女儿普莉娅的想法，脸上露出不悦。他头顶浮现出洗碗的图标，他严厉地指着普莉娅（Priya），8岁，印度女孩，眼神从渴望变为害怕，穿着破旧的黄色连衣裙式上衣，命令她去做家务。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '727',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 13:08:21 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': 'b2a8bb34-5922-4373-b043-f825de2d101e',
  'x-fal-served-from': '11d71c81-8b4b-4bd9-af20-c2223dedc3be',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10913584008812904
🖼️ 生成的图片URL: https://v3.fal.media/files/lion/POOO16IFNmObhWDRH3WgK.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1015ms
存储图片: https://v3.fal.media/files/lion/POOO16IFNmObhWDRH3WgK.jpeg 分镜ID: 5e7eaae9-a185-40ea-b94d-e1b28e34aae7
图片已保存到本地: /images/shot-5e7eaae9-a185-40ea-b94d-e1b28e34aae7-1753967303703.jpg
 POST /api/store-image 200 in 2202ms
 ✓ Compiled in 637ms (539 modules)
 GET /storyboards 200 in 51ms
🚀 Schnell API 请求开始
📝 提示词: [中景镜头，光线昏暗，印度乡村破旧的厨房。普莉娅（Priya），8岁，印度女孩，表情悲伤，穿着破旧的黄色连衣裙式上衣，站在一个简陋的水泥台前，费力地清洗着家里的碗碟。背景是简陋的厨房，细节丰富，电影质感，4K画质。]
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "[中景镜头，光线昏暗，印度乡村破旧的厨房。普莉娅（Priya），8岁，印度女孩，表情悲伤，穿着破旧的黄色连衣裙式上衣，站在一个简陋的水泥台前，费力地清洗着家里的碗碟。背景是简陋的厨房，细节丰富，电影质感，4K画质。]",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/penguin/8fPTKl9srYWvG8Q0V-coi.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10806331783533096
  },
  "seed": 156931764,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "[中景镜头，光线昏暗，印度乡村破旧的厨房。普莉娅（Priya），8岁，印度女孩，表情悲伤，穿着破旧的黄色连衣裙式上衣，站在一个简陋的水泥台前，费力地清洗着家里的碗碟。背景是简陋的厨房，细节丰富，电影质感，4K画质。]"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '543',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 13:08:30 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '11a13e10-8d40-4ddb-8f23-21351c1f4eac',
  'x-fal-served-from': 'dfb274dd-ca2a-460a-b32d-a6d3e5476a37',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10806331783533096
🖼️ 生成的图片URL: https://v3.fal.media/files/penguin/8fPTKl9srYWvG8Q0V-coi.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1047ms
存储图片: https://v3.fal.media/files/penguin/8fPTKl9srYWvG8Q0V-coi.jpeg 分镜ID: a84b1908-bc2a-4f7c-b79b-f0629164e212
图片已保存到本地: /images/shot-a84b1908-bc2a-4f7c-b79b-f0629164e212-1753967312621.jpg
 POST /api/store-image 200 in 1939ms
 ✓ Compiled in 290ms (539 modules)
 GET /storyboards 200 in 18ms
 ✓ Compiled / in 484ms (643 modules)
 GET / 200 in 879ms
 ○ Compiling /video-download ...
 ✓ Compiled /video-download in 570ms (652 modules)
 GET /video-download 200 in 721ms
 GET /storyboards 200 in 29ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 2.2s (1167 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET /_next/static/webpack/54472d7dddd13345.webpack.hot-update.json 404 in 1271ms
 GET /storyboards 200 in 96ms
 GET /storyboards 200 in 373ms
 ✓ Compiled in 1603ms (1181 modules)
 GET /storyboards 200 in 281ms
 ○ Compiling /api/flux ...
 ✓ Compiled /api/flux in 2.4s (1181 modules)
FLUX_API_KEY: 存在
生成图片提示词: 一位欧美女性站着弯下腰，双手撑地，她身穿一件粉色运动背心和深灰色瑜伽裤。一只哈士奇站在她的后背上，另一只哈士奇站在她后面（女性头向着镜头，挡住了站在她后面的哈士奇）。明亮的客厅背景，有窗户和绿植。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/e4/42838811cfb2d1/11cfb2d195204cd2b4972a5efcaecc4a/sample.png?se=2025-07-31T14%3A27%3A09Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=f/E7Lg8jrhA4IXrKZbc93%2B9k7Vf%2BjRm7Hzq2hYf89%2B8%3D","revised_prompt":"A Western woman standing bent over, hands on the ground, wearing a pink sports tank top and dark gray yoga pants. One husky stands on her back, another husky stands behind her (the woman's head faces the camera, blocking the husky standing behind her). Bright living room background with windows and green plants."}],"created":1753971430}
 POST /api/flux 200 in 14400ms
 ○ Compiling /api/store-image ...
 ✓ Compiled /api/store-image in 3.1s (659 modules)
存储图片: https://delivery-eu1.bfl.ai/results/e4/42838811cfb2d1/11cfb2d195204cd2b4972a5efcaecc4a/sample.png?se=2025-07-31T14%3A27%3A09Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=f/E7Lg8jrhA4IXrKZbc93%2B9k7Vf%2BjRm7Hzq2hYf89%2B8%3D 分镜ID: 0ddc46e3-39a7-47eb-b86d-bccdedf17d1f
图片已保存到本地: /images/shot-0ddc46e3-39a7-47eb-b86d-bccdedf17d1f-1753971438187.png
 POST /api/store-image 200 in 6804ms
 ✓ Compiled in 1414ms (539 modules)
 GET /storyboards 200 in 241ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，柔和的晨光，明亮的客厅，欧美女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，身体开始向下弯曲，双手逐渐靠近地面，表情专注。两只哈士奇停止转圈，好奇地看着她。背景是宽敞明亮的客厅，落地窗外是绿色的植物，窗帘是米色的，客厅里有米色沙发和木质茶几。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/eb/449338bc10721f/bc10721ffa8c4ce98c7ab0eace4a9d2d/sample.png?se=2025-07-31T14%3A27%3A30Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=wa1g/V7OSQhXHVbNikfShjoMCpyD4dxJBH7rPbIDNaQ%3D","revised_prompt":"Medium shot, soft morning light, bright living room, Western female, blonde hair and blue eyes, 28 years old, wearing a pink sports tank top and dark gray yoga pants, body beginning to bend down, hands gradually approaching the floor, focused expression. Two Huskies stop spinning, looking at her curiously. The background is a spacious and bright living room, floor-to-ceiling windows with green plants outside, beige curtains, beige sofa and wooden coffee table in the living room. Ultra HD, rich details, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753971451}
 POST /api/flux 200 in 11393ms
存储图片: https://delivery-us1.bfl.ai/results/eb/449338bc10721f/bc10721ffa8c4ce98c7ab0eace4a9d2d/sample.png?se=2025-07-31T14%3A27%3A30Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=wa1g/V7OSQhXHVbNikfShjoMCpyD4dxJBH7rPbIDNaQ%3D 分镜ID: 00ec3629-8cee-484e-9972-53cf84e88ecb
图片已保存到本地: /images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png
 POST /api/store-image 200 in 2577ms
 ✓ Compiled in 840ms (539 modules)
 GET /storyboards 200 in 69ms
FLUX_API_KEY: 存在
生成图片提示词: 特写镜头，柔和的晨光，明亮的客厅，棕白色哈士奇，前爪搭在欧美女性的背上，好奇地看着她。女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，身体弯曲，表情略微惊讶。背景虚化，突出哈士奇和女性。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/ec/46310527c63bdd/27c63bdd4697449c9b4738072d318528/sample.png?se=2025-07-31T14%3A27%3A44Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=PyUGv4zyhZHaJDpsNhzauOcLFE2aAiPN8SS0PP93GYc%3D","revised_prompt":"Close-up shot, soft morning light, bright living room, brown and white husky, front paws resting on the back of a Western woman, curiously looking at her. Woman, blonde hair and blue eyes, 28 years old, wearing a pink sports tank top and dark gray yoga pants, body bent, expression slightly surprised. Background blurred, emphasizing the husky and the woman. Ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753971466}
 POST /api/flux 200 in 9248ms
存储图片: https://delivery-eu4.bfl.ai/results/ec/46310527c63bdd/27c63bdd4697449c9b4738072d318528/sample.png?se=2025-07-31T14%3A27%3A44Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=PyUGv4zyhZHaJDpsNhzauOcLFE2aAiPN8SS0PP93GYc%3D 分镜ID: f8ba42a5-9a41-402c-a870-fdc63b59243b
图片已保存到本地: /images/shot-f8ba42a5-9a41-402c-a870-fdc63b59243b-1753971469128.png
 POST /api/store-image 200 in 2858ms
 ✓ Compiled in 759ms (539 modules)
 GET /storyboards 200 in 63ms
FLUX_API_KEY: 存在
生成图片提示词: 动态抓拍，柔和的晨光，明亮的客厅，灰白色哈士奇，正在跳跃，试图跳到欧美女性的背上。女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，身体弯曲，表情惊讶。背景是宽敞明亮的客厅，落地窗外是绿色的植物，窗帘是米色的，客厅里有米色沙发和木质茶几。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/dd/477948ac7544d4/ac7544d4b4d14e96a10ccea60d07ec26/sample.png?se=2025-07-31T14%3A27%3A59Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=lkdick2ozSQXAqfqPsRbSiIiz7AYQ7Vgr67cn9vltd0%3D","revised_prompt":"Dynamic snapshot, soft morning light, bright living room, gray and white husky, jumping, trying to jump onto the back of a Western woman. Woman, blonde hair and blue eyes, 28 years old, wearing a pink sports tank top and dark gray yoga pants, body bent, expression surprised. The background is a spacious and bright living room, with green plants outside the floor-to-ceiling windows, beige curtains, beige sofa and wooden coffee table in the living room. Ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753971480}
 POST /api/flux 200 in 9535ms
存储图片: https://delivery-eu4.bfl.ai/results/dd/477948ac7544d4/ac7544d4b4d14e96a10ccea60d07ec26/sample.png?se=2025-07-31T14%3A27%3A59Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=lkdick2ozSQXAqfqPsRbSiIiz7AYQ7Vgr67cn9vltd0%3D 分镜ID: 92cd976e-132d-4b18-9e1e-707977a2e516
图片已保存到本地: /images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png
 POST /api/store-image 200 in 3073ms
 ✓ Compiled in 743ms (539 modules)
 GET /storyboards 200 in 133ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，柔和的晨光，明亮的客厅，欧美女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，身体弯曲，两只哈士奇站在她的背上，一只棕白色，一只灰白色，女性表情略微吃力但带着笑容。背景是宽敞明亮的客厅，落地窗外是绿色的植物，窗帘是米色的，客厅里有米色沙发和木质茶几。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/ed/493231270c8bb0/270c8bb0dff147a6930f33582287b391/sample.png?se=2025-07-31T14%3A28%3A14Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=Ax2bmEjWkZywBJAAVKjiNIV4TKJ7mpj1VOOFtr8UTgg%3D","revised_prompt":"medium shot, soft morning light, bright living room, Western female, blonde hair and blue eyes, 28 years old, wearing a pink sports tank top and dark gray yoga pants, body bent, two huskies standing on her back, one brown and white, one gray and white, female expression slightly strained but smiling. Background is a spacious and bright living room, floor-to-ceiling windows with green plants outside, beige curtains, beige sofa and wooden coffee table in the living room. ultra high definition, rich details, cinematic texture, photo-realistic, cinematic lighting, 4K resolution."}],"created":1753971494}
 POST /api/flux 200 in 8784ms
存储图片: https://delivery-us1.bfl.ai/results/ed/493231270c8bb0/270c8bb0dff147a6930f33582287b391/sample.png?se=2025-07-31T14%3A28%3A14Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=Ax2bmEjWkZywBJAAVKjiNIV4TKJ7mpj1VOOFtr8UTgg%3D 分镜ID: 18ab5351-2ed4-47f1-aa22-64109013afcd
图片已保存到本地: /images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png
 POST /api/store-image 200 in 2243ms
 ✓ Compiled in 621ms (539 modules)
 GET /storyboards 200 in 72ms
FLUX_API_KEY: 存在
生成图片提示词: 特写镜头，柔和的晨光，明亮的客厅，欧美女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，抬头看向镜头，露出灿烂的笑容。两只哈士奇，一只棕白色，一只灰白色，也好奇地看向镜头。背景虚化，突出人物和动物的表情。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/d7/508368ba47770a/ba47770a771b44a485a03cc2fadfaccf/sample.png?se=2025-07-31T14%3A28%3A29Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=sTNbqUQFVjwF609dE9n1DcfgFdCcOQulQaMxt6Yf%2BJ0%3D","revised_prompt":"Close-up shot, soft morning light, bright living room, Western female, blonde hair and blue eyes, 28 years old, wearing a pink sports vest and dark gray yoga pants, looking up at the camera with a radiant smile. Two huskies, one brown and white, one gray and white, also curiously looking at the camera. Background blurred, emphasizing the expressions of the person and animals. Ultra HD, rich details, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753971511}
 POST /api/flux 200 in 12094ms
存储图片: https://delivery-us1.bfl.ai/results/d7/508368ba47770a/ba47770a771b44a485a03cc2fadfaccf/sample.png?se=2025-07-31T14%3A28%3A29Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=sTNbqUQFVjwF609dE9n1DcfgFdCcOQulQaMxt6Yf%2BJ0%3D 分镜ID: 1b648b86-5a4d-439f-acdb-de6ff3dc90ed
图片已保存到本地: /images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png
 POST /api/store-image 200 in 2323ms
 ✓ Compiled in 450ms (539 modules)
 GET /storyboards 200 in 837ms
FLUX_API_KEY: 存在
生成图片提示词: 电影感长镜头，柔和的晨光，阳光明媚的客厅，欧美女性，金发碧眼，28岁，身穿粉色运动背心和深灰色瑜伽裤，身体弯曲，两只哈士奇站在她的背上，一只棕白色，一只灰白色，女性表情放松。背景是宽敞明亮的客厅，落地窗外是绿色的植物，窗帘是米色的，客厅里有米色沙发和木质茶几，阳光洒进房间，营造温馨的氛围。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/8c/52355320881368/20881368c94e41289fb532fca568d007/sample.png?se=2025-07-31T14%3A28%3A44Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=gVFaXXB75MZcK2grN3T2u3TxTv9DianNvqCl7v%2Bl2EQ%3D","revised_prompt":"Cinematic long shot, soft morning light, sunny living room, Western female, blonde hair and blue eyes, 28 years old, wearing a pink sports tank top and dark gray yoga pants, body bent, two huskies standing on her back, one brown and white, one gray and white, female expression relaxed. The background is a spacious and bright living room, floor-to-ceiling windows with green plants outside, beige curtains, beige sofa and wooden coffee table in the living room, sunlight pouring into the room creating a warm atmosphere. Ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753971525}
 POST /api/flux 200 in 9590ms
存储图片: https://delivery-eu1.bfl.ai/results/8c/52355320881368/20881368c94e41289fb532fca568d007/sample.png?se=2025-07-31T14%3A28%3A44Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=gVFaXXB75MZcK2grN3T2u3TxTv9DianNvqCl7v%2Bl2EQ%3D 分镜ID: a68b89ac-fbfe-4fa9-b072-dfccaceb0686
图片已保存到本地: /images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png
 POST /api/store-image 200 in 2651ms
 ✓ Compiled in 311ms (539 modules)
 GET /storyboards 200 in 49ms
FLUX_API_KEY: 存在
生成图片提示词: 电影感长镜头，柔和的晨光，阳光明媚的客厅，欧美女性，金发碧眼，28岁，身穿浅蓝色运动上衣和深灰色瑜伽裤，身体弯曲，两只哈士奇站在她的背上，一只棕白色，一只灰白色，女性表情放松。背景是宽敞明亮的客厅，落地窗外是绿色的植物，窗帘是米色的，客厅里有米色沙发和木质茶几，阳光洒进房间，营造温馨的氛围。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/42/54009729d6f453/29d6f45327e44b8b9a122f343b0a6ad8/sample.png?se=2025-07-31T14%3A29%3A01Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=Ye48RJ5NBaGaxw8k56Q6z8bFtRlOaeJkLauWWt/lxFE%3D","revised_prompt":"Cinematic long shot, soft morning light, sunny living room, Western woman, blonde hair and blue eyes, 28 years old, wearing a light blue sports top and dark gray yoga pants, body bent, two huskies standing on her back, one brown and white, one gray and white, woman’s expression relaxed. Background is a spacious and bright living room, floor-to-ceiling windows with green plants outside, beige curtains, beige sofa and wooden coffee table in the living room, sunlight streaming into the room, creating a warm atmosphere. Ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753971542}
 POST /api/flux 200 in 12162ms
存储图片: https://delivery-us1.bfl.ai/results/42/54009729d6f453/29d6f45327e44b8b9a122f343b0a6ad8/sample.png?se=2025-07-31T14%3A29%3A01Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=Ye48RJ5NBaGaxw8k56Q6z8bFtRlOaeJkLauWWt/lxFE%3D 分镜ID: eeb293a4-4b0e-459a-8bf2-973770144a77
图片已保存到本地: /images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png
 POST /api/store-image 200 in 2529ms
 ✓ Compiled in 555ms (539 modules)
 GET /storyboards 200 in 84ms
 ✓ Compiled /api/download-image in 420ms (1186 modules)
代理下载图片: /images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png'
  }
}
 POST /api/download-image 500 in 633ms
代理下载图片: /images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png'
  }
}
 POST /api/download-image 500 in 162ms
代理下载图片: /images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png'
  }
}
 POST /api/download-image 500 in 92ms
代理下载图片: /images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png'
  }
}
 POST /api/download-image 500 in 146ms
代理下载图片: /images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png'
  }
}
 POST /api/download-image 500 in 87ms
代理下载图片: /images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png'
  }
}
 POST /api/download-image 500 in 130ms
 ✓ Compiled in 3.2s (1177 modules)
 GET /storyboards 200 in 223ms
 ✓ Compiled in 1724ms (1177 modules)
 GET /storyboards 200 in 643ms
 ✓ Compiled in 3.4s (1177 modules)
 GET /storyboards 200 in 126ms
 ✓ Compiled in 2s (1177 modules)
 GET /storyboards 200 in 62ms
 ✓ Compiled /api/download-image in 340ms (656 modules)
代理下载图片: /images/shot-0ddc46e3-39a7-47eb-b86d-bccdedf17d1f-1753971438187.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-0ddc46e3-39a7-47eb-b86d-bccdedf17d1f-1753971438187.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-0ddc46e3-39a7-47eb-b86d-bccdedf17d1f-1753971438187.png'
  }
}
 POST /api/download-image 500 in 645ms
代理下载图片: /images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-00ec3629-8cee-484e-9972-53cf84e88ecb-1753971454710.png'
  }
}
 POST /api/download-image 500 in 247ms
代理下载图片: /images/shot-f8ba42a5-9a41-402c-a870-fdc63b59243b-1753971469128.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-f8ba42a5-9a41-402c-a870-fdc63b59243b-1753971469128.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-f8ba42a5-9a41-402c-a870-fdc63b59243b-1753971469128.png'
  }
}
 POST /api/download-image 500 in 188ms
代理下载图片: /images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-92cd976e-132d-4b18-9e1e-707977a2e516-1753971483843.png'
  }
}
 POST /api/download-image 500 in 107ms
代理下载图片: /images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-18ab5351-2ed4-47f1-aa22-64109013afcd-1753971497314.png'
  }
}
 POST /api/download-image 500 in 217ms
代理下载图片: /images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-1b648b86-5a4d-439f-acdb-de6ff3dc90ed-1753971513916.png'
  }
}
 POST /api/download-image 500 in 195ms
代理下载图片: /images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-a68b89ac-fbfe-4fa9-b072-dfccaceb0686-1753971528439.png'
  }
}
 POST /api/download-image 500 in 143ms
代理下载图片: /images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-eeb293a4-4b0e-459a-8bf2-973770144a77-1753971545134.png'
  }
}
 POST /api/download-image 500 in 122ms
 ✓ Compiled /api/schnell in 355ms (658 modules)
🚀 Schnell API 请求开始
📝 提示词: 电影感全景镜头，柔和的晨光，现代印度别墅门口，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿橄榄绿色的印度陆军常服，庄严地向家人告别。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，抱着女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，眼中流露着不舍。一辆军用吉普车在旁等候。背景是装饰精美的别墅和郁郁葱葱的花园。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感全景镜头，柔和的晨光，现代印度别墅门口，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿橄榄绿色的印度陆军常服，庄严地向家人告别。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，抱着女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，眼中流露着不舍。一辆军用吉普车在旁等候。背景是装饰精美的别墅和郁郁葱葱的花园。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/penguin/z4G8ikoaGF5PELjxNKbFj.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10934109333902597
  },
  "seed": 998590737,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感全景镜头，柔和的晨光，现代印度别墅门口，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿橄榄绿色的印度陆军常服，庄严地向家人告别。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，抱着女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，眼中流露着不舍。一辆军用吉普车在旁等候。背景是装饰精美的别墅和郁郁葱葱的花园。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '913',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:22:33 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '35e26ba8-459d-42a6-930a-20433ccc2460',
  'x-fal-served-from': 'f6125931-71e3-46dc-819b-fcd06b480c4c',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10934109333902597
🖼️ 生成的图片URL: https://v3.fal.media/files/penguin/z4G8ikoaGF5PELjxNKbFj.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1627ms
 ✓ Compiled /api/store-image in 145ms (661 modules)
存储图片: https://v3.fal.media/files/penguin/z4G8ikoaGF5PELjxNKbFj.jpeg 分镜ID: 2d4d1ad8-68b3-489a-94ec-0af6bc19be90
图片已保存到本地: /images/shot-2d4d1ad8-68b3-489a-94ec-0af6bc19be90-1753971756664.jpg
 POST /api/store-image 200 in 3208ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，白天，阳光明媚，繁华的印度街头市场，色彩鲜艳。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，蹲下身，微笑着为女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，整理头发，Anjali手里拿着一串糖葫芦。背景是熙熙攘攘的人群、色彩鲜艳的货摊、各种印度特色小吃和商品。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，白天，阳光明媚，繁华的印度街头市场，色彩鲜艳。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，蹲下身，微笑着为女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，整理头发，Anjali手里拿着一串糖葫芦。背景是熙熙攘攘的人群、色彩鲜艳的货摊、各种印度特色小吃和商品。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
 ✓ Compiled in 6.2s (539 modules)
 GET /storyboards 200 in 431ms
❌ 网络请求失败: {
  name: 'TypeError',
  message: 'fetch failed',
  stack: 'TypeError: fetch failed\n' +
    '    at node:internal/deps/undici/undici:13510:13\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async POST (webpack-internal:///(rsc)/./app/api/schnell/route.ts:68:36)\n' +
    '    at async AppRouteRouteModule.do (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:33891)\n' +
    '    at async AppRouteRouteModule.handle (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41254)\n' +
    '    at async doRender (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:1513:42)\n' +
    '    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:1915:28)\n' +
    '    at async DevServer.renderPageComponent (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:2393:24)\n' +
    '    at async DevServer.renderToResponseImpl (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:2430:32)\n' +
    '    at async DevServer.pipeImpl (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:1003:25)\n' +
    '    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/next-server.js:304:17)\n' +
    '    at async DevServer.handleRequestImpl (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/base-server.js:895:17)\n' +
    '    at async /Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n' +
    '    at async Span.traceAsyncFn (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/trace/trace.js:157:20)\n' +
    '    at async DevServer.handleRequest (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n' +
    '    at async invokeRender (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/lib/router-server.js:235:21)\n' +
    '    at async handleRequest (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/lib/router-server.js:426:24)\n' +
    '    at async requestHandlerImpl (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/lib/router-server.js:450:13)\n' +
    '    at async Server.requestListener (/Users/<USER>/Documents/GitHub/ScriptVivid-AI/node_modules/next/dist/server/lib/start-server.js:158:13)'
}
❌ Schnell API 调用失败: TypeError: fetch failed
    at async POST (app/api/schnell/route.ts:70:29)
  68 |       console.log("📤 发送请求体:", JSON.stringify(requestBody, null, 2))
  69 |
> 70 |       const submitResponse = await fetch("https://fal.run/fal-ai/flux/schnell", {
     |                             ^
  71 |         method: "POST",
  72 |         headers: {
  73 |           "Authorization": `Key ${SCHNELL_API_KEY}`, {
  [cause]: [Error [ConnectTimeoutError]: Connect Timeout Error (attempted address: fal.run:443, timeout: 10000ms)] {
    code: 'UND_ERR_CONNECT_TIMEOUT'
  }
}
 POST /api/schnell 500 in 13961ms
🚀 Schnell API 请求开始
📝 提示词: 电影感特写镜头，白天，光线充足，市场的一个角落。Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他身后的两个手下，流里流气的印度青年，穿着破旧的T恤。他们正用贪婪和不怀好意的眼神，远远地盯着前方。背景是拥挤的市场人群和杂乱的货摊。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感特写镜头，白天，光线充足，市场的一个角落。Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他身后的两个手下，流里流气的印度青年，穿着破旧的T恤。他们正用贪婪和不怀好意的眼神，远远地盯着前方。背景是拥挤的市场人群和杂乱的货摊。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/kangaroo/uxYRYjtfnW0jgPD47FAHP.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11847099289298058
  },
  "seed": *********,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感特写镜头，白天，光线充足，市场的一个角落。Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他身后的两个手下，流里流气的印度青年，穿着破旧的T恤。他们正用贪婪和不怀好意的眼神，远远地盯着前方。背景是拥挤的市场人群和杂乱的货摊。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '754',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:22:56 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '3b4299a8-cdc5-4278-b222-1fc1030d22e1',
  'x-fal-served-from': '17466e3a-8814-40eb-8a07-8f3d361823e4',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11847099289298058
🖼️ 生成的图片URL: https://v3.fal.media/files/kangaroo/uxYRYjtfnW0jgPD47FAHP.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1454ms
存储图片: https://v3.fal.media/files/kangaroo/uxYRYjtfnW0jgPD47FAHP.jpeg 分镜ID: 2c301790-acd7-43eb-89dd-61f879582eff
图片已保存到本地: /images/shot-2c301790-acd7-43eb-89dd-61f879582eff-1753971779071.jpg
 POST /api/store-image 200 in 2471ms
 ✓ Compiled in 3.2s (539 modules)
 GET /storyboards 200 in 260ms
🚀 Schnell API 请求开始
📝 提示词: 电影感动态抓拍，白天，光线昏暗，一条昏暗狭窄的小巷里。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，表情惊恐，被Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，粗暴地抓住手臂，她尽力将女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，护在身后，另一名混混，流里流气的印度青年，穿着破旧的T恤，抓住女儿的手。背景是破旧的墙壁和垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感动态抓拍，白天，光线昏暗，一条昏暗狭窄的小巷里。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，表情惊恐，被Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，粗暴地抓住手臂，她尽力将女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，护在身后，另一名混混，流里流气的印度青年，穿着破旧的T恤，抓住女儿的手。背景是破旧的墙壁和垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/tiger/gQiRR7mGw2Dwx0y1N2go4.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10958907753229141
  },
  "seed": 588402571,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感动态抓拍，白天，光线昏暗，一条昏暗狭窄的小巷里。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，表情惊恐，被Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，粗暴地抓住手臂，她尽力将女儿Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，护在身后，另一名混混，流里流气的印度青年，穿着破旧的T恤，抓住女儿的手。背景是破旧的墙壁和垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '973',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:03 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': 'b60faab8-3428-4cf8-b505-af47119b5532',
  'x-fal-served-from': 'f6125931-71e3-46dc-819b-fcd06b480c4c',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10958907753229141
🖼️ 生成的图片URL: https://v3.fal.media/files/tiger/gQiRR7mGw2Dwx0y1N2go4.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1235ms
存储图片: https://v3.fal.media/files/tiger/gQiRR7mGw2Dwx0y1N2go4.jpeg 分镜ID: 087be069-7a3b-473f-98ac-18d31c915495
图片已保存到本地: /images/shot-087be069-7a3b-473f-98ac-18d31c915495-1753971786353.jpg
 POST /api/store-image 200 in 2511ms
 ✓ Compiled in 766ms (539 modules)
 GET /storyboards 200 in 87ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，光线昏暗，废弃仓库的肮脏角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，衣服上沾了灰尘，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，Anjali害怕地埋在妈妈怀里哭。背景是破旧的墙壁、生锈的铁桶和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，光线昏暗，废弃仓库的肮脏角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，衣服上沾了灰尘，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，Anjali害怕地埋在妈妈怀里哭。背景是破旧的墙壁、生锈的铁桶和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/kangaroo/Av5t_cewL5Ba88yoLsrT-.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11752873298246413
  },
  "seed": 262830479,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，光线昏暗，废弃仓库的肮脏角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，衣服上沾了灰尘，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，Anjali害怕地埋在妈妈怀里哭。背景是破旧的墙壁、生锈的铁桶和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '767',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:09 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '8bd2bf3f-2b0b-4fdb-b1f4-afe69ffde3cd',
  'x-fal-served-from': 'f20b6bf6-f934-4c65-9cda-2d98212a3187',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11752873298246413
🖼️ 生成的图片URL: https://v3.fal.media/files/kangaroo/Av5t_cewL5Ba88yoLsrT-.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1499ms
存储图片: https://v3.fal.media/files/kangaroo/Av5t_cewL5Ba88yoLsrT-.jpeg 分镜ID: bd4a43dc-41ed-4d22-999f-db5ebfd006de
图片已保存到本地: /images/shot-bd4a43dc-41ed-4d22-999f-db5ebfd006de-1753971791696.jpg
 POST /api/store-image 200 in 1750ms
 ✓ Compiled in 1928ms (539 modules)
 GET /storyboards 200 in 58ms
🚀 Schnell API 请求开始
📝 提示词: 电影感特写镜头，光线昏暗，废弃仓库的角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，脸上挂着泪痕，躲在一个生锈的油桶后面，她将手机紧紧贴在耳边，用尽全力压低声音，嘴唇颤抖着，向电话那头的丈夫求救，眼神里充满了绝望和最后一丝希望。背景是生锈的油桶和昏暗的仓库环境。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感特写镜头，光线昏暗，废弃仓库的角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，脸上挂着泪痕，躲在一个生锈的油桶后面，她将手机紧紧贴在耳边，用尽全力压低声音，嘴唇颤抖着，向电话那头的丈夫求救，眼神里充满了绝望和最后一丝希望。背景是生锈的油桶和昏暗的仓库环境。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/zebra/iKfysR9y3RNbfSzq8nzYO.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.1197964996099472
  },
  "seed": 1730854065,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感特写镜头，光线昏暗，废弃仓库的角落。Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，脸上挂着泪痕，躲在一个生锈的油桶后面，她将手机紧紧贴在耳边，用尽全力压低声音，嘴唇颤抖着，向电话那头的丈夫求救，眼神里充满了绝望和最后一丝希望。背景是生锈的油桶和昏暗的仓库环境。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '772',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:16 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '66f3a4fb-c73d-40d4-8f0d-6a828a9165b2',
  'x-fal-served-from': 'd251f751-1676-4eed-b58c-30ebbd71a43d',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.1197964996099472
🖼️ 生成的图片URL: https://v3.fal.media/files/zebra/iKfysR9y3RNbfSzq8nzYO.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1921ms
存储图片: https://v3.fal.media/files/zebra/iKfysR9y3RNbfSzq8nzYO.jpeg 分镜ID: 49d84ef0-1d06-45ce-84ad-d4013f00d2d2
图片已保存到本地: /images/shot-49d84ef0-1d06-45ce-84ad-d4013f00d2d2-1753971798561.jpg
 POST /api/store-image 200 in 2345ms
 ✓ Compiled in 3.7s (539 modules)
🚀 Schnell API 请求开始
📝 提示词: 电影感特写镜头，白天，光线充足，军事基地。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时穿着迷彩作战服，手持电话，听到消息后，他坚毅的脸上布满寒霜，眼神充满了杀气和愤怒。背景是忙碌的军事基地和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感特写镜头，白天，光线充足，军事基地。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时穿着迷彩作战服，手持电话，听到消息后，他坚毅的脸上布满寒霜，眼神充满了杀气和愤怒。背景是忙碌的军事基地和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
 GET /storyboards 200 in 71ms
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/koala/NOV1J9mKyEzgtYdDoi4z2.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11735771223902702
  },
  "seed": 536828866,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感特写镜头，白天，光线充足，军事基地。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时穿着迷彩作战服，手持电话，听到消息后，他坚毅的脸上布满寒霜，眼神充满了杀气和愤怒。背景是忙碌的军事基地和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '665',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:25 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '95f9c67b-d26f-4407-96c2-4acebeb2b774',
  'x-fal-served-from': '1a90fd76-574f-431a-b997-aa388f29f077',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11735771223902702
🖼️ 生成的图片URL: https://v3.fal.media/files/koala/NOV1J9mKyEzgtYdDoi4z2.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 3587ms
存储图片: https://v3.fal.media/files/koala/NOV1J9mKyEzgtYdDoi4z2.jpeg 分镜ID: c758d147-425f-48aa-8d91-38e48a447d27
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/14.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/14.pack.gz'
存储图片失败: TypeError: fetch failed
    at async POST (app/api/store-image/route.ts:16:21)
  14 |
  15 |     // 下载图片
> 16 |     const response = await fetch(imageUrl, {
     |                     ^
  17 |       headers: {
  18 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  19 |       } {
  [cause]: [Error [ConnectTimeoutError]: Connect Timeout Error (attempted address: v3.fal.media:443, timeout: 10000ms)] {
    code: 'UND_ERR_CONNECT_TIMEOUT'
  }
}
 POST /api/store-image 500 in 11776ms
🚀 Schnell API 请求开始
📝 提示词: 电影感广角镜头，白天，阳光明媚，印度马路上，一长列橄榄绿色的军用卡车和装甲车队正卷起尘土，快速前进，三架印度陆军的武装直升机呈战斗队形，场面宏大，充满力量感，展现出强大的军事压迫感。背景是广阔的印度平原和蓝天白云。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感广角镜头，白天，阳光明媚，印度马路上，一长列橄榄绿色的军用卡车和装甲车队正卷起尘土，快速前进，三架印度陆军的武装直升机呈战斗队形，场面宏大，充满力量感，展现出强大的军事压迫感。背景是广阔的印度平原和蓝天白云。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/tiger/DEq5H-kg5aMuMi9u3qy-d.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11727069783955812
  },
  "seed": 401354164,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感广角镜头，白天，阳光明媚，印度马路上，一长列橄榄绿色的军用卡车和装甲车队正卷起尘土，快速前进，三架印度陆军的武装直升机呈战斗队形，场面宏大，充满力量感，展现出强大的军事压迫感。背景是广阔的印度平原和蓝天白云。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '650',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:41 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '7541f94d-9b3e-4df1-be88-0716ba96fdf0',
  'x-fal-served-from': '542bc753-fc43-4a47-87e8-e36d29ebaebc',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11727069783955812
🖼️ 生成的图片URL: https://v3.fal.media/files/tiger/DEq5H-kg5aMuMi9u3qy-d.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1193ms
存储图片: https://v3.fal.media/files/tiger/DEq5H-kg5aMuMi9u3qy-d.jpeg 分镜ID: 98873eb5-6c26-4cb6-9b96-4b667ee1d762
图片已保存到本地: /images/shot-98873eb5-6c26-4cb6-9b96-4b667ee1d762-1753971824315.jpg
 POST /api/store-image 200 in 2512ms
 ✓ Compiled in 740ms (539 modules)
 GET /storyboards 200 in 80ms
🚀 Schnell API 请求开始
📝 提示词: 电影感鸟瞰视角，夜晚，光线强烈，废弃仓库的鸟瞰视角。十几辆军车停在仓库前面，刺眼的车灯全部打开。天空中一架直升机用巨大的探照灯光柱罩住整个建筑，3名士兵正沿着光柱从天而降，场面极度震撼。背景是漆黑的夜空和荒凉的郊外。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感鸟瞰视角，夜晚，光线强烈，废弃仓库的鸟瞰视角。十几辆军车停在仓库前面，刺眼的车灯全部打开。天空中一架直升机用巨大的探照灯光柱罩住整个建筑，3名士兵正沿着光柱从天而降，场面极度震撼。背景是漆黑的夜空和荒凉的郊外。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/elephant/eq5MforLIau89S9XnWj4Y.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.1176238339394331
  },
  "seed": 1272677105,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感鸟瞰视角，夜晚，光线强烈，废弃仓库的鸟瞰视角。十几辆军车停在仓库前面，刺眼的车灯全部打开。天空中一架直升机用巨大的探照灯光柱罩住整个建筑，3名士兵正沿着光柱从天而降，场面极度震撼。背景是漆黑的夜空和荒凉的郊外。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '654',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:48 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '11874ddd-c0a8-461e-b1a7-9c749de7bcf4',
  'x-fal-served-from': '8c472a5f-62b0-44c3-9129-20cad3b2d4d6',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.1176238339394331
🖼️ 生成的图片URL: https://v3.fal.media/files/elephant/eq5MforLIau89S9XnWj4Y.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1093ms
存储图片: https://v3.fal.media/files/elephant/eq5MforLIau89S9XnWj4Y.jpeg 分镜ID: d58337ef-b272-4250-aff7-a39e1b2cb289
图片已保存到本地: /images/shot-d58337ef-b272-4250-aff7-a39e1b2cb289-1753971830764.jpg
 POST /api/store-image 200 in 2394ms
 ✓ Compiled in 862ms (539 modules)
 GET /storyboards 200 in 98ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线强烈，仓库大门被猛地踹开。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时身穿全套黑色特种作战装备，手持步枪，第一个冲入，眼神冷酷，他身后紧跟着一队全副武装的士兵，战术动作专业而迅速。背景是破旧的仓库内部和刺眼的车灯光芒。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库大门被猛地踹开。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时身穿全套黑色特种作战装备，手持步枪，第一个冲入，眼神冷酷，他身后紧跟着一队全副武装的士兵，战术动作专业而迅速。背景是破旧的仓库内部和刺眼的车灯光芒。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/rabbit/iGZMEeRum7egjIb0rDBb0.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10951686790212989
  },
  "seed": 1956853256,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库大门被猛地踹开。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，此时身穿全套黑色特种作战装备，手持步枪，第一个冲入，眼神冷酷，他身后紧跟着一队全副武装的士兵，战术动作专业而迅速。背景是破旧的仓库内部和刺眼的车灯光芒。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '739',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:23:54 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '329a035d-ff6b-46cc-b1b3-19e8457490fb',
  'x-fal-served-from': '8c472a5f-62b0-44c3-9129-20cad3b2d4d6',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10951686790212989
🖼️ 生成的图片URL: https://v3.fal.media/files/rabbit/iGZMEeRum7egjIb0rDBb0.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1073ms
存储图片: https://v3.fal.media/files/rabbit/iGZMEeRum7egjIb0rDBb0.jpeg 分镜ID: 71fb927f-ce09-40eb-b8b8-89fee84d14d0
图片已保存到本地: /images/shot-71fb927f-ce09-40eb-b8b8-89fee84d14d0-1753971836436.jpg
 POST /api/store-image 200 in 2183ms
 ✓ Compiled in 3.8s (539 modules)
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他的手下，流里流气的印度青年，穿着破旧的T恤，被强光照射，他们惊恐地举起双手，被十几个黑洞洞的枪口指着，脸上是难以置信的恐惧，与周围的士兵形成了鲜明对比。背景是破旧的仓库内部和全副武装的士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他的手下，流里流气的印度青年，穿着破旧的T恤，被强光照射，他们惊恐地举起双手，被十几个黑洞洞的枪口指着，脸上是难以置信的恐惧，与周围的士兵形成了鲜明对比。背景是破旧的仓库内部和全副武装的士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
 GET /storyboards 200 in 70ms
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/kangaroo/kiKqCG82e0vcttJfEXsMr.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10899578593671322
  },
  "seed": 1225609632,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和他的手下，流里流气的印度青年，穿着破旧的T恤，被强光照射，他们惊恐地举起双手，被十几个黑洞洞的枪口指着，脸上是难以置信的恐惧，与周围的士兵形成了鲜明对比。背景是破旧的仓库内部和全副武装的士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '833',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:24:02 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '94c85507-10ea-43fc-a83f-9d53436d8a3b',
  'x-fal-served-from': '8c472a5f-62b0-44c3-9129-20cad3b2d4d6',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10899578593671322
🖼️ 生成的图片URL: https://v3.fal.media/files/kangaroo/kiKqCG82e0vcttJfEXsMr.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1632ms
存储图片: https://v3.fal.media/files/kangaroo/kiKqCG82e0vcttJfEXsMr.jpeg 分镜ID: 717bb4e0-85f8-460f-8c19-ea4cb7c5f462
图片已保存到本地: /images/shot-717bb4e0-85f8-460f-8c19-ea4cb7c5f462-1753971844304.jpg
 POST /api/store-image 200 in 2100ms
 ✓ Compiled in 2.1s (539 modules)
 GET /storyboards 200 in 77ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，面对面站着。背景是Raka的几名手下举起双手。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，面对面站着。背景是Raka的几名手下举起双手。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/koala/t13PZuWnxb4xc5iUckbHc.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.10843494907021523
  },
  "seed": 1779745247,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库内部，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，和Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，面对面站着。背景是Raka的几名手下举起双手。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '746',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:24:08 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': 'd97f175d-bdd2-4581-90da-c1ea7cdc3fa9',
  'x-fal-served-from': '8b0c9d7f-9a0b-4e06-a60e-290f16e8d997',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.10843494907021523
🖼️ 生成的图片URL: https://v3.fal.media/files/koala/t13PZuWnxb4xc5iUckbHc.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1776ms
存储图片: https://v3.fal.media/files/koala/t13PZuWnxb4xc5iUckbHc.jpeg 分镜ID: b005cbed-fa17-4260-8f4e-50ded8076d0f
图片已保存到本地: /images/shot-b005cbed-fa17-4260-8f4e-50ded8076d0f-1753971850746.jpg
 POST /api/store-image 200 in 2112ms
 ✓ Compiled in 856ms (539 modules)
 GET /storyboards 200 in 310ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线昏暗，废弃仓库的肮脏角落，一个充满温情的画面。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，单膝跪地，用军刀割断了妻女身上的绳索，泣不成声的Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，静静看着。背景是破旧的墙壁和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线昏暗，废弃仓库的肮脏角落，一个充满温情的画面。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，单膝跪地，用军刀割断了妻女身上的绳索，泣不成声的Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，静静看着。背景是破旧的墙壁和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/koala/v5pZftEnpqglR4WNT0279.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.1096213087439537
  },
  "seed": 347437965,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线昏暗，废弃仓库的肮脏角落，一个充满温情的画面。Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，单膝跪地，用军刀割断了妻女身上的绳索，泣不成声的Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，被绳子捆在一起，静静看着。背景是破旧的墙壁和散落的垃圾。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '949',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:24:14 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '18abd30e-b03c-4daf-9c04-1b8c3c7c0ea8',
  'x-fal-served-from': '542bc753-fc43-4a47-87e8-e36d29ebaebc',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.1096213087439537
🖼️ 生成的图片URL: https://v3.fal.media/files/koala/v5pZftEnpqglR4WNT0279.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1200ms
存储图片: https://v3.fal.media/files/koala/v5pZftEnpqglR4WNT0279.jpeg 分镜ID: 2864b050-aa67-4a59-ad8f-389409dca9f9
图片已保存到本地: /images/shot-2864b050-aa67-4a59-ad8f-389409dca9f9-1753971856487.jpg
 POST /api/store-image 200 in 1992ms
 ✓ Compiled in 707ms (539 modules)
 GET /storyboards 200 in 248ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线强烈，仓库外，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，已经被打得鼻青脸肿和他的手下，流里流气的印度青年，穿着破旧的T恤，两人都双手被反绑，正被两名高大的士兵押着，准备上一辆军用卡车的后车厢。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库外，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，已经被打得鼻青脸肿和他的手下，流里流气的印度青年，穿着破旧的T恤，两人都双手被反绑，正被两名高大的士兵押着，准备上一辆军用卡车的后车厢。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/tiger/FXk-OUYG96xrUE1qj85Su.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.117799480445683
  },
  "seed": 502037289,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线强烈，仓库外，Raka，面相凶恶的印度混混头目，皮肤黝黑，穿着一件紧身的黑色背心，露出手臂上的纹身，搭配褪色的牛仔裤，已经被打得鼻青脸肿和他的手下，流里流气的印度青年，穿着破旧的T恤，两人都双手被反绑，正被两名高大的士兵押着，准备上一辆军用卡车的后车厢。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '764',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:24:20 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': 'ef27372f-7b29-4d56-824e-ff542a56fc6c',
  'x-fal-served-from': '17466e3a-8814-40eb-8a07-8f3d361823e4',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.117799480445683
🖼️ 生成的图片URL: https://v3.fal.media/files/tiger/FXk-OUYG96xrUE1qj85Su.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 1728ms
存储图片: https://v3.fal.media/files/tiger/FXk-OUYG96xrUE1qj85Su.jpeg 分镜ID: 3694d9c0-438d-40c8-879d-6e1865758536
图片已保存到本地: /images/shot-3694d9c0-438d-40c8-879d-6e1865758536-1753971862217.jpg
 POST /api/store-image 200 in 1778ms
 ✓ Compiled in 963ms (539 modules)
 GET /storyboards 200 in 59ms
🚀 Schnell API 请求开始
📝 提示词: 电影感中景镜头，夜晚，光线强烈，最终画面，在闪烁的军车灯光背景下，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，将Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，紧紧地搂在怀中，他的背影坚实可靠，宛如一座山，守护着他的整个世界。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
📐 图片尺寸: 9:16
🔄 映射后的图片尺寸: portrait_16_9
📤 发送请求体: {
  "prompt": "电影感中景镜头，夜晚，光线强烈，最终画面，在闪烁的军车灯光背景下，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，将Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，紧紧地搂在怀中，他的背影坚实可靠，宛如一座山，守护着他的整个世界。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "image_size": "portrait_16_9",
  "num_inference_steps": 4,
  "guidance_scale": 3.5,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
✅ Schnell API 响应成功
📊 完整响应: {
  "images": [
    {
      "url": "https://v3.fal.media/files/elephant/Za5nJZU9j23MiCxIvAyp1.jpeg",
      "width": 576,
      "height": 1024,
      "content_type": "image/jpeg"
    }
  ],
  "timings": {
    "inference": 0.11859746184200048
  },
  "seed": 1940320452,
  "has_nsfw_concepts": [
    false
  ],
  "prompt": "电影感中景镜头，夜晚，光线强烈，最终画面，在闪烁的军车灯光背景下，Major Raj，英俊的印度陆军军官，身材挺拔，面容坚毅，留着修剪整齐的胡子，身穿全套黑色特种作战装备，将Priya，美丽的印度女子，黑色长发，穿着一件明黄色的印花库尔塔上衣和白色紧身裤，和Anjali，可爱的印度小女孩，扎着马尾辫，穿着蓝色牛仔背带裙和白色小T恤，紧紧地搂在怀中，他的背影坚实可靠，宛如一座山，守护着他的整个世界。背景是军车和士兵。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。"
}
📍 响应头: {
  connection: 'keep-alive',
  'content-length': '918',
  'content-type': 'application/json',
  date: 'Thu, 31 Jul 2025 14:24:24 GMT',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'x-fal-billable-units': '1',
  'x-fal-request-id': '558ae51b-0cbf-428e-a18f-1b15b54cca45',
  'x-fal-served-from': '3728a90e-d29e-466b-a27c-df2336638cca',
  'x-robots-tag': 'noindex'
}
⏱️ 生成时间: 0.11859746184200048
🖼️ 生成的图片URL: https://v3.fal.media/files/elephant/Za5nJZU9j23MiCxIvAyp1.jpeg
🎯 Schnell API 调用完成
 POST /api/schnell 200 in 751ms
存储图片: https://v3.fal.media/files/elephant/Za5nJZU9j23MiCxIvAyp1.jpeg 分镜ID: 576a2776-4df3-4af7-86e6-6fc4f31d3961
图片已保存到本地: /images/shot-576a2776-4df3-4af7-86e6-6fc4f31d3961-1753971866687.jpg
 POST /api/store-image 200 in 1817ms
 ✓ Compiled in 339ms (539 modules)
 GET /storyboards 200 in 37ms
 ○ Compiling /api/gemini ...
 ✓ Compiled /api/gemini in 977ms (1181 modules)
GEMINI_API_KEY: 存在
GEMINI_API_URL: https://openrouter.ai/api/v1/chat/completions
OpenRouter 响应: {
  "id": "gen-**********-7rBrDf5wkH0ug5EgWs53",
  "provider": "Google",
  "model": "google/gemini-2.0-flash-001",
  "object": "chat.completion",
  "created": **********,
  "choices": [
    {
      "logprobs": null,
      "finish_reason": "stop",
      "native_finish_reason": "STOP",
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "好的，现在我将根据您提供的视频文字脚本，将每一个场景拆解成独立的图片，并为每张图片生成详细的、用于图像生成模型的提示词。\n\n**分镜1**\n分镜内容：一个暴雨的夜晚，在一条潮湿狭窄的印度小巷里，路面反射着昏黄的灯光。普里亚一手打着一把破旧的紫色雨伞，一手温柔地抱着一个襁褓中的婴儿，她低头看着婴儿，眼神中充满了绝望、心碎和不舍。\n图片提示词：电影感镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，昏黄的灯光，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着褪色但依然能看出款式的紫色紧身连衣裙），一手打着破旧的紫色雨伞，一手抱着襁褓中的婴儿，绝望的眼神，心碎的表情，不舍的情绪，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, beautiful, purple dress, rainy Indian alley, holding baby, desperate look, slow motion.\n字幕：（无）\n\n**分镜2**\n分镜内容：在同一条下着雨的印度小巷里，普里亚泪流满面地将怀里的婴儿递给她的母亲卡马拉。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。\n图片提示词：特写镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，昏黄的灯光，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着褪色但依然能看出款式的紫色紧身连衣裙），泪流满面，递出婴儿，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），接过婴儿，普里亚头顶出现想象气泡，气泡中有一枚硬币，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, beautiful, purple dress, Kamala, old woman, brown sari, rainy Indian alley, handing over baby, tears, close-up.\n字幕：（无）\n\n**分镜3**\n分镜内容：在雨中，卡马拉紧紧地抱着襁褓中的外孙女玛雅，用自己的莎丽为她挡雨，眼神坚定，充满了保护欲。\n图片提示词：中景镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），紧紧抱着襁褓中的玛雅（襁褓中的印度女婴），用莎丽挡雨，坚定的眼神，保护欲，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, brown sari, rainy Indian alley, holding baby Maya, protective look, tracking shot.\n字幕：（无）\n\n**分镜4**\n分镜内容：在一个陈旧但温暖的印度室内，墙壁斑驳。卡马拉坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，她的脸上带着慈爱的微笑。\n图片提示词：中景镜头，光线昏暗，印度室内，陈旧的墙壁，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），坐在破旧沙发上，用奶瓶喂养玛雅（襁褓中的印度女婴），慈爱的微笑，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, brown sari, Indian home, feeding baby Maya, loving smile, slow zoom.\n字幕：（无）\n\n**分镜5**\n分镜内容：几年后，在尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉背着一个装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。\n图片提示词：广角镜头，白天，阳光明媚，尘土飞扬，印度街头，杂乱的建筑，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），背着装满塑料瓶的麻袋，捡拾废品，吃力的表情，汗水，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, worn sari, Indian street, collecting trash, tired look, wide shot.\n字幕：（无）\n\n**分镜6**\n分镜内容：在一家明亮的印度儿童服装店里，卡马拉脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。\n图片提示词：中景镜头，白天，阳光明媚，印度儿童服装店，明亮的光线，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），慈祥的微笑，取下柠檬黄公主裙，白色蝴蝶结，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, worn sari, Indian clothing store, buying yellow dress, happy smile, close-up.\n字幕：（无）\n\n**分镜7**\n分镜内容：回到简陋的家中，卡马拉将黄色的公主裙递给小女孩玛雅，玛雅接过裙子，脸上绽放出无比惊喜和灿烂的笑容。\n图片提示词：中景镜头，白天，印度室内，简陋的家，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），递出黄色公主裙，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，继承了母亲的绝美容貌），接过裙子，惊喜的笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, worn sari, Maya, little girl, yellow dress, Indian home, happy reaction, medium shot.\n字幕：（无）\n\n**分镜8**\n分镜内容：在洒满阳光的房间地板上，周围放着许多编织好的小篮子。卡马拉和穿着黄色公主裙的小女孩玛雅坐在一起，卡马拉正手把手地教她编织篮子。\n图片提示词：中景镜头，白天，阳光明媚，印度室内，地板上，编织好的篮子，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙），一起编织篮子，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, worn sari, Maya, little girl, yellow dress, Indian home, weaving baskets, sunny day, medium shot.\n字幕：（无）\n\n**分镜9**\n分镜内容：在一个简陋的印度乡村学校门口，卡马拉蹲下身子，为穿着黄色公主裙、背着书包的小女孩玛雅整理衣领，满眼都是骄傲和慈爱。\n图片提示词：中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old woman, worn sari, Maya, little girl, yellow dress, Indian school, adjusting collar, proud look, medium shot.\n字幕：（无）\n\n**分镜10**\n分镜内容：在一个有窗户的简陋舞蹈练习室里，少女玛雅穿着一身带有孔雀羽毛元素的华丽黄色舞裙，正在优雅地旋转跳舞，裙摆飞扬。\n图片提示词：中景镜头，白天，阳光明媚，简陋的舞蹈练习室，少女玛雅（约16岁的印度女孩，身材高挑，初具性感魅力，扎着马尾辫），华丽的黄色舞裙，孔雀羽毛元素，旋转跳舞，裙摆飞扬，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, teenage girl, yellow dress, peacock feathers, Indian dance studio, spinning, graceful, medium shot.\n字幕：（无）\n\n**分镜11**\n分镜内容：在灯光璀璨的舞台上，背景虚化。年轻的玛雅穿着那身华丽的孔雀舞裙，面带自信的微笑，高高举起一座金色的奖杯。\n图片提示词：中景镜头，夜晚，舞台，灯光璀璨，背景虚化，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信），华丽的孔雀舞裙，自信的微笑，高举金色奖杯，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow dress, peacock feathers, stage, bright lights, holding trophy, confident smile, medium shot.\n字幕：（无）\n\n**分镜12**\n分镜内容：在一个漂亮的别墅外，阳光明媚。年轻的玛雅张开双臂，激动地拥抱着向她走来的母亲普里亚，一旁的卡马拉看着她们，脸上露出欣慰的泪水和笑容。\n图片提示词：中景镜头，白天，阳光明媚，漂亮的别墅外，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），张开双臂，普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），走过来，拥抱，卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），欣慰的泪水和笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow dress, Priya, middle-aged woman, Kamala, old woman, villa, sunny day, hugging, happy tears, medium shot.\n字幕：（无）\n\n**分镜13**\n分镜内容：三代女人手拉着手，微笑着一起走进她们的漂亮新家。走在中间的年轻的玛雅，左手牵着她的母亲普里亚，右手牵着她的外祖母卡马拉。\n图片提示词：中景镜头，白天，阳光明媚，漂亮的别墅外，三代女人手拉着手，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），微笑，走进新家，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow dress, Priya, middle-aged woman, Kamala, old woman, villa, sunny day, holding hands, walking into home, medium shot.\n字幕：（无）\n\n**分镜14**\n分镜内容：在新家舒适的客厅沙发上，三代女人幸福地依偎在一起。年轻的玛雅坐在中间，一边是她的母亲普里亚，另一边是她的外祖母卡马拉。\n图片提示词：中景镜头，白天，阳光明媚，新家客厅，舒适的沙发，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，已换上漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），依偎在一起，幸福的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, casual clothes, Priya, middle-aged woman, Kamala, old woman, villa, sunny day, sitting on sofa, happy, medium shot.\n字幕：（无）\n\n**分镜15**\n分镜内容：一张幸福的三代人合影特写。年轻的玛雅、她的母亲普里亚和她的外祖母卡马拉紧紧挨着，都对着镜头露出灿烂、释怀而幸福的笑容，画面其乐融融，充满了希望。\n图片提示词：特写镜头，白天，阳光明媚，新家客厅，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），紧紧挨着，灿烂的笑容，释怀的表情，幸福的笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, casual clothes, Priya, middle-aged woman, Kamala, old woman, villa, sunny day, close-up, smiling, happy family.\n字幕：（无）\n",
        "refusal": null,
        "reasoning": null
      }
    }
  ],
  "usage": {
    "prompt_tokens": 3124,
    "completion_tokens": 3330,
    "total_tokens": 6454
  }
}
 POST /api/gemini 200 in 22722ms
 ✓ Compiled /api/flux in 469ms (658 modules)
FLUX_API_KEY: 存在
生成图片提示词: 电影感镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，昏黄的灯光，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着褪色但依然能看出款式的紫色紧身连衣裙），一手打着破旧的紫色雨伞，一手抱着襁褓中的婴儿，绝望的眼神，心碎的表情，不舍的情绪，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/e0/491846f25fe195/f25fe195060645ef94d1f8d073c521f6/sample.png?se=2025-07-31T15%3A01%3A32Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=QhVXNlpPPwLHupjJPHuzSmCOtFevaJ1nrakn7SxIQ9I%3D","revised_prompt":"cinematic shot, dim lighting, heavy rain, Indian alley, slippery road surface, yellowish lighting, Priya (about 25-year-old Indian woman, hot and sexy figure, stunningly beautiful appearance, long curly hair, wearing a faded but still recognizable style purple tight dress), holding a worn purple umbrella in one hand, cradling a swaddled baby in the other hand, desperate eyes, heartbroken expression, reluctant emotion, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753973494}
 POST /api/flux 200 in 14561ms
 ○ Compiling /api/store-image ...
 ✓ Compiled /api/store-image in 670ms (659 modules)
存储图片: https://delivery-eu1.bfl.ai/results/e0/491846f25fe195/f25fe195060645ef94d1f8d073c521f6/sample.png?se=2025-07-31T15%3A01%3A32Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=QhVXNlpPPwLHupjJPHuzSmCOtFevaJ1nrakn7SxIQ9I%3D 分镜ID: a1cc945e-1b6c-4437-bde8-bcd36468cb93
图片已保存到本地: /images/shot-a1cc945e-1b6c-4437-bde8-bcd36468cb93-1753973499275.png
 POST /api/store-image 200 in 4693ms
 ✓ Compiled in 4.9s (539 modules)
FLUX_API_KEY: 存在
生成图片提示词: 特写镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，昏黄的灯光，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着褪色但依然能看出款式的紫色紧身连衣裙），泪流满面，递出婴儿，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），接过婴儿，普里亚头顶出现想象气泡，气泡中有一枚硬币，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
 GET /storyboards 200 in 573ms
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/3b/5118526211bfa5/6211bfa5958a4a05b8d09ba945f03b0b/sample.png?se=2025-07-31T15%3A01%3A53Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=oMm/HnMZDPerbqDAegIttKg125vG5JmrFsOx%2BTUNUsU%3D","revised_prompt":"Close-up shot, dim lighting, heavy rain, Indian alley, slippery road, yellowish light, Priya (about 25-year-old Indian woman, hot and sexy figure, stunningly beautiful, long curly hair, wearing a faded but still recognizable style purple tight dress), tears streaming down her face, handing over a baby, Kamala (about 60-year-old Indian elderly woman, kind face but full of wrinkles, wearing a simple faded brown sari), receiving the baby, thought bubble appearing above Priya's head, with a coin inside the bubble, ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753973513}
 POST /api/flux 200 in 9736ms
存储图片: https://delivery-us1.bfl.ai/results/3b/5118526211bfa5/6211bfa5958a4a05b8d09ba945f03b0b/sample.png?se=2025-07-31T15%3A01%3A53Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=oMm/HnMZDPerbqDAegIttKg125vG5JmrFsOx%2BTUNUsU%3D 分镜ID: b284b400-20bc-48d4-a2a9-a41ff7e2b2fa
图片已保存到本地: /images/shot-b284b400-20bc-48d4-a2a9-a41ff7e2b2fa-1753973516652.png
 POST /api/store-image 200 in 2632ms
 ✓ Compiled in 2.2s (539 modules)
 GET /storyboards 200 in 79ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），紧紧抱着襁褓中的玛雅（襁褓中的印度女婴），用莎丽挡雨，坚定的眼神，保护欲，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/a8/526152e5257117/e5257117144f4e2fa9851d6e5e049f0d/sample.png?se=2025-07-31T15%3A02%3A07Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=RkPzrNmEh7UBShnez2SKRyps%2BVKc8ihmmuMw%2BXYJtYM%3D","revised_prompt":"Medium shot, dim lighting, heavy rain, Indian alley, slippery road, Kamala (around 60-year-old Indian woman, kind-faced but wrinkled, wearing a simple faded earthy brown sari), tightly holding baby Maya (Indian baby girl in swaddling clothes), using sari to shield from rain, determined eyes, protective instinct, ultra HD, rich details, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753973527}
 POST /api/flux 200 in 8007ms
存储图片: https://delivery-us1.bfl.ai/results/a8/526152e5257117/e5257117144f4e2fa9851d6e5e049f0d/sample.png?se=2025-07-31T15%3A02%3A07Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=RkPzrNmEh7UBShnez2SKRyps%2BVKc8ihmmuMw%2BXYJtYM%3D 分镜ID: 0d01739e-0337-45ce-8422-578a22aba3e9
图片已保存到本地: /images/shot-0d01739e-0337-45ce-8422-578a22aba3e9-1753973530554.png
 POST /api/store-image 200 in 2542ms
 ✓ Compiled in 2.3s (539 modules)
 GET /storyboards 200 in 391ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，光线昏暗，印度室内，陈旧的墙壁，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色莎丽），坐在破旧沙发上，用奶瓶喂养玛雅（襁褓中的印度女婴），慈爱的微笑，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/df/540753bad72300/bad72300d93445b8b7c7a6e21b4afff5/sample.png?se=2025-07-31T15%3A02%3A21Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=xsXHnWbVtzrHbKH7TscVwv2xzlpLm1SbikU08gJ6a/4%3D","revised_prompt":"Medium shot, dim lighting, Indian interior, old walls, Kamala (approximately 60-year-old Indian woman, kind-faced but wrinkled, wearing a simple faded tan sari), sitting on a worn-out sofa, feeding Maya (an Indian baby girl in swaddling clothes) with a milk bottle, loving smile, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753973542}
 POST /api/flux 200 in 8474ms
存储图片: https://delivery-us1.bfl.ai/results/df/540753bad72300/bad72300d93445b8b7c7a6e21b4afff5/sample.png?se=2025-07-31T15%3A02%3A21Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=xsXHnWbVtzrHbKH7TscVwv2xzlpLm1SbikU08gJ6a/4%3D 分镜ID: 7f753925-afdf-4505-9b1a-8f202a6e460d
图片已保存到本地: /images/shot-7f753925-afdf-4505-9b1a-8f202a6e460d-1753973546042.png
 POST /api/store-image 200 in 3571ms
 ✓ Compiled in 814ms (539 modules)
 GET /storyboards 200 in 89ms
FLUX_API_KEY: 存在
生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬，印度街头，杂乱的建筑，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），背着装满塑料瓶的麻袋，捡拾废品，吃力的表情，汗水，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/c6/555050d8c7d0e0/d8c7d0e09c874b1eb7beec867b884155/sample.png?se=2025-07-31T15%3A02%3A36Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=ehUkPOW%2BWMJCYEGWB%2BSs%2BulXoSmtKI8L8GiaMYmxCLE%3D","revised_prompt":"wide-angle lens, daytime, bright sunshine, dust flying, streets of India, chaotic buildings, Kamala (about 65-year-old Indian woman, older appearance, wearing a more worn-out earth-brown sari), carrying a sack full of plastic bottles, scavenging for recyclables, strained expression, sweat, ultra high-definition, rich in detail, cinematic texture, photo-realistic, cinematic lighting effects, 4K resolution."}],"created":1753973557}
 POST /api/flux 200 in 8628ms
存储图片: https://delivery-eu4.bfl.ai/results/c6/555050d8c7d0e0/d8c7d0e09c874b1eb7beec867b884155/sample.png?se=2025-07-31T15%3A02%3A36Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=ehUkPOW%2BWMJCYEGWB%2BSs%2BulXoSmtKI8L8GiaMYmxCLE%3D 分镜ID: 149dd0d6-dc6a-47cf-9d21-3ecb2b5b62bb
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/1.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/1.pack.gz'
存储图片失败: TypeError: fetch failed
    at async POST (app/api/store-image/route.ts:16:21)
  14 |
  15 |     // 下载图片
> 16 |     const response = await fetch(imageUrl, {
     |                     ^
  17 |       headers: {
  18 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  19 |       } {
  [cause]: [Error [SocketError]: other side closed] {
    code: 'UND_ERR_SOCKET',
    socket: {
      localAddress: '**********',
      localPort: 57987,
      remoteAddress: '************',
      remotePort: 443,
      remoteFamily: 'IPv4',
      timeout: undefined,
      bytesWritten: 482,
      bytesRead: 0
    }
  }
}
 POST /api/store-image 500 in 77089ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度儿童服装店，明亮的光线，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），慈祥的微笑，取下柠檬黄公主裙，白色蝴蝶结，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/45/644357f43a002c/f43a002c93e14259addc46f7e49757f2/sample.png?se=2025-07-31T15%3A04%3A05Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=RD1yVi6mdHGHhKBvXbfT42WvB%2B4dLE6NN9o1r/kvI3w%3D","revised_prompt":"Medium shot, daytime, bright sunlight, Indian children's clothing store, bright lighting, Kamala (about 65-year-old Indian elderly woman, with an older-looking face, wearing a more worn-out earth-brown sari), kind smile, taking off a lemon yellow princess dress, white bow, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753973646}
 POST /api/flux 200 in 11877ms
存储图片: https://delivery-eu1.bfl.ai/results/45/644357f43a002c/f43a002c93e14259addc46f7e49757f2/sample.png?se=2025-07-31T15%3A04%3A05Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=RD1yVi6mdHGHhKBvXbfT42WvB%2B4dLE6NN9o1r/kvI3w%3D 分镜ID: 4d16b81b-ae82-438a-a821-c85a1936762a
图片已保存到本地: /images/shot-4d16b81b-ae82-438a-a821-c85a1936762a-1753973651927.png
 POST /api/store-image 200 in 3143ms
 ✓ Compiled in 2.4s (539 modules)
 GET /storyboards 200 in 53ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，印度室内，简陋的家，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），递出黄色公主裙，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，继承了母亲的绝美容貌），接过裙子，惊喜的笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/24/661903691f4043/691f404341734b6b8e6907f377e76aa3/sample.png?se=2025-07-31T15%3A04%3A23Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=BJ7TPRT0n3YKWu7JLUqtrzcqHnn8AxfZjQhxXozSICA%3D","revised_prompt":"Medium shot, daytime, Indian indoor, humble home, Kamala (approximately 65-year-old Indian elderly woman, with an even older appearance, wearing a more worn-out earth-brown sari), handing out a yellow princess dress, little girl Maya (approximately 6-7 years old Indian girl, lively and cute, big eyes, long hair, inheriting her mother's exceptional beauty), receiving the dress, surprised smile, ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753973664}
 POST /api/flux 200 in 11967ms
存储图片: https://delivery-eu4.bfl.ai/results/24/661903691f4043/691f404341734b6b8e6907f377e76aa3/sample.png?se=2025-07-31T15%3A04%3A23Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=BJ7TPRT0n3YKWu7JLUqtrzcqHnn8AxfZjQhxXozSICA%3D 分镜ID: 71d48cde-a287-4858-bcd4-56f5a6af9aa0
图片已保存到本地: /images/shot-71d48cde-a287-4858-bcd4-56f5a6af9aa0-1753973669963.png
 POST /api/store-image 200 in 3364ms
 ✓ Compiled in 2.4s (539 modules)
 GET /storyboards 200 in 33ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度室内，地板上，编织好的篮子，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙），一起编织篮子，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/5d/67861572b3d022/72b3d022467b445490788fedd4b063cf/sample.png?se=2025-07-31T15%3A04%3A39Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=svXXmBA7Fa%2Bq53kBQ9DiY8yk598vcu9K957VyH78lMs%3D","revised_prompt":"Medium shot, daytime, sunny, Indian indoor, on the floor, woven baskets, Kamala (about 65-year-old Indian elderly woman, with an older-looking face, wearing a more worn-out earth-brown sari), little girl Maya (about 6-7 years old Indian girl, lively and cute, big eyes, long hair, wearing a yellow princess dress), weaving baskets together, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753973681}
 POST /api/flux 200 in 8815ms
存储图片: https://delivery-us1.bfl.ai/results/5d/67861572b3d022/72b3d022467b445490788fedd4b063cf/sample.png?se=2025-07-31T15%3A04%3A39Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=svXXmBA7Fa%2Bq53kBQ9DiY8yk598vcu9K957VyH78lMs%3D 分镜ID: 7966dd7f-18d2-498a-b14a-e1ceb4eff2bc
图片已保存到本地: /images/shot-7966dd7f-18d2-498a-b14a-e1ceb4eff2bc-1753973683716.png
 POST /api/store-image 200 in 2451ms
 ✓ Compiled in 1407ms (539 modules)
 GET /storyboards 200 in 536ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225446909060756OriKrvlh)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225446909060756OriKrvlh)","type":"rix_api_error"}}
 POST /api/flux 500 in 815ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，简陋的舞蹈练习室，少女玛雅（约16岁的印度女孩，身材高挑，初具性感魅力，扎着马尾辫），华丽的黄色舞裙，孔雀羽毛元素，旋转跳舞，裙摆飞扬，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225448211847193Rycgut8i)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225448211847193Rycgut8i)","type":"rix_api_error"}}
 POST /api/flux 500 in 285ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，夜晚，舞台，灯光璀璨，背景虚化，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信），华丽的孔雀舞裙，自信的微笑，高举金色奖杯，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225449494288735OXbzyR72)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225449494288735OXbzyR72)","type":"rix_api_error"}}
 POST /api/flux 500 in 271ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），张开双臂，普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），走过来，拥抱，卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），欣慰的泪水和笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312254507791415611WDyQF1Z)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312254507791415611WDyQF1Z)","type":"rix_api_error"}}
 POST /api/flux 500 in 271ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外，三代女人手拉着手，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），微笑，走进新家，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 2025073122545257241193UoOyhhWA)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 2025073122545257241193UoOyhhWA)","type":"rix_api_error"}}
 POST /api/flux 500 in 269ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，新家客厅，舒适的沙发，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，已换上漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），依偎在一起，幸福的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225453458579069gUtWqbhL)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225453458579069gUtWqbhL)","type":"rix_api_error"}}
 POST /api/flux 500 in 275ms
FLUX_API_KEY: 存在
生成图片提示词: 特写镜头，白天，阳光明媚，新家客厅，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），紧紧挨着，灿烂的笑容，释怀的表情，幸福的笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225454745001049t2ZHV8mr)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225454745001049t2ZHV8mr)","type":"rix_api_error"}}
 POST /api/flux 500 in 268ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312256036519055rMZHXV1s)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312256036519055rMZHXV1s)","type":"rix_api_error"}}
 POST /api/flux 500 in 835ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，简陋的舞蹈练习室，少女玛雅（约16岁的印度女孩，身材高挑，初具性感魅力，扎着马尾辫），华丽的黄色舞裙，孔雀羽毛元素，旋转跳舞，裙摆飞扬，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312256115168375119WgIWNfu)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312256115168375119WgIWNfu)","type":"rix_api_error"}}
 POST /api/flux 500 in 1536ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225826677048039sRo8U1ah)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731225826677048039sRo8U1ah)","type":"rix_api_error"}}
 POST /api/flux 500 in 841ms
 ✓ Compiled /api/download-image in 376ms (658 modules)
代理下载图片: /images/shot-a1cc945e-1b6c-4437-bde8-bcd36468cb93-1753973499275.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-a1cc945e-1b6c-4437-bde8-bcd36468cb93-1753973499275.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-a1cc945e-1b6c-4437-bde8-bcd36468cb93-1753973499275.png'
  }
}
 POST /api/download-image 500 in 595ms
代理下载图片: /images/shot-b284b400-20bc-48d4-a2a9-a41ff7e2b2fa-1753973516652.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-b284b400-20bc-48d4-a2a9-a41ff7e2b2fa-1753973516652.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-b284b400-20bc-48d4-a2a9-a41ff7e2b2fa-1753973516652.png'
  }
}
 POST /api/download-image 500 in 213ms
代理下载图片: /images/shot-0d01739e-0337-45ce-8422-578a22aba3e9-1753973530554.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-0d01739e-0337-45ce-8422-578a22aba3e9-1753973530554.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-0d01739e-0337-45ce-8422-578a22aba3e9-1753973530554.png'
  }
}
 POST /api/download-image 500 in 104ms
代理下载图片: /images/shot-7f753925-afdf-4505-9b1a-8f202a6e460d-1753973546042.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-7f753925-afdf-4505-9b1a-8f202a6e460d-1753973546042.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-7f753925-afdf-4505-9b1a-8f202a6e460d-1753973546042.png'
  }
}
 POST /api/download-image 500 in 114ms
代理下载图片: https://delivery-eu4.bfl.ai/results/c6/555050d8c7d0e0/d8c7d0e09c874b1eb7beec867b884155/sample.png?se=2025-07-31T15%3A02%3A36Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=ehUkPOW%2BWMJCYEGWB%2BSs%2BulXoSmtKI8L8GiaMYmxCLE%3D
图片获取成功，大小: 1913473 类型: image/png
 POST /api/download-image 200 in 2646ms
代理下载图片: /images/shot-4d16b81b-ae82-438a-a821-c85a1936762a-1753973651927.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-4d16b81b-ae82-438a-a821-c85a1936762a-1753973651927.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-4d16b81b-ae82-438a-a821-c85a1936762a-1753973651927.png'
  }
}
 POST /api/download-image 500 in 113ms
代理下载图片: /images/shot-71d48cde-a287-4858-bcd4-56f5a6af9aa0-1753973669963.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-71d48cde-a287-4858-bcd4-56f5a6af9aa0-1753973669963.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-71d48cde-a287-4858-bcd4-56f5a6af9aa0-1753973669963.png'
  }
}
 POST /api/download-image 500 in 191ms
代理下载图片: /images/shot-7966dd7f-18d2-498a-b14a-e1ceb4eff2bc-1753973683716.png
代理下载图片错误: TypeError: Failed to parse URL from /images/shot-7966dd7f-18d2-498a-b14a-e1ceb4eff2bc-1753973683716.png
    at POST (app/api/download-image/route.ts:14:27)
  12 |
  13 |     // 代理获取图片
> 14 |     const response = await fetch(imageUrl, {
     |                           ^
  15 |       headers: {
  16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  17 |       } {
  [cause]: TypeError: Invalid URL
      at POST (app/api/download-image/route.ts:14:27)
    12 |
    13 |     // 代理获取图片
  > 14 |     const response = await fetch(imageUrl, {
       |                           ^
    15 |       headers: {
    16 |         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    17 |       } {
    code: 'ERR_INVALID_URL',
    input: '/images/shot-7966dd7f-18d2-498a-b14a-e1ceb4eff2bc-1753973683716.png'
  }
}
 POST /api/download-image 500 in 96ms
 ○ Compiling /api/doubao ...
 ✓ Compiled /api/doubao in 1630ms (1185 modules)
ARK_API_KEY: 存在
豆包生成图片提示词: 电影感镜头，光线昏暗，暴雨，印度小巷，湿滑的路面，昏黄的灯光，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着褪色但依然能看出款式的紫色紧身连衣裙），一手打着破旧的紫色雨伞，一手抱着襁褓中的婴儿，绝望的眼神，心碎的表情，不舍的情绪，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753973965,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397396235614a1c1340d8ef0a4017eaad09f96acf48328d0.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T145925Z&X-Tos-Expires=86400&X-Tos-Signature=b88335c7c5a2e2af22d76bd45b068f8164c3e6e133d8f0e367721b93bcdb3926&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 5559ms
 ✓ Compiled /api/store-image in 461ms (663 modules)
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397396235614a1c1340d8ef0a4017eaad09f96acf48328d0.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T145925Z&X-Tos-Expires=86400&X-Tos-Signature=b88335c7c5a2e2af22d76bd45b068f8164c3e6e133d8f0e367721b93bcdb3926&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: a1cc945e-1b6c-4437-bde8-bcd36468cb93
图片已保存到本地: /images/shot-a1cc945e-1b6c-4437-bde8-bcd36468cb93-1753973967198.jpg
 POST /api/store-image 200 in 1634ms
 ✓ Compiled in 1377ms (539 modules)
 GET /storyboards 200 in 170ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 9.5s (1177 modules)
 GET /storyboards 200 in 305ms
 ✓ Compiled in 7.9s (1177 modules)
 GET /storyboards 200 in 122ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 2.5s (1177 modules)
 GET /storyboards 200 in 78ms
 GET /storyboards 200 in 565ms
 ✓ Compiled /api/flux in 453ms (656 modules)
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731230310662932093Trs2RWoB)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731230310662932093Trs2RWoB)","type":"rix_api_error"}}
 POST /api/flux 500 in 2880ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 1605ms (1199 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 2217ms
 GET /storyboards 200 in 48ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312303373165869836SHyH7y4)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 202507312303373165869836SHyH7y4)","type":"rix_api_error"}}
 POST /api/flux 500 in 996ms
FLUX_API_KEY: 存在
生成图片提示词: [动态抓拍，光线昏暗，印度乡村破旧的房间内。拉詹（Rajan），40岁，印度男人，表情愤怒，穿着深棕色衬衫，突然出现，一把夺过书，愤怒地训斥着普莉娅（Priya），8岁，印度女孩，表情惊恐，眼含泪水，穿着破旧的黄色连衣裙式上衣。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。]
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731230405587982887qgrXhfz2)","type":"rix_api_error"}}
FLUX API error: {"error":{"message":"[sk-P36***AvC] Token amount has been exhausted token.RemainQuota = -40000 (request id: 20250731230405587982887qgrXhfz2)","type":"rix_api_error"}}
 POST /api/flux 500 in 814ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，印度乡村学校门口，简陋的学校，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），蹲下身子，小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，穿着黄色公主裙，背着书包），整理衣领，骄傲的眼神，慈爱的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/0b/5388743b3ad386/3b3ad38603564c9a941474636507728d/sample.png?se=2025-07-31T15%3A18%3A59Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=FFu6PKcpCuhFNiwTAZsMySpFlVe9cQ4piz5aGrXpog8%3D","revised_prompt":"Medium shot, daytime, sunny, in front of a rural school in India, a simple school, Kamala (about a 65-year-old Indian woman, her face older, wearing a more worn-out dusty brown sari) crouching down, little girl Maya (about a 6-7-year-old Indian girl, lively and cute, big eyes, long hair, wearing a yellow princess dress, carrying a backpack), adjusting her collar, proud eyes, loving expression, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753974541}
 POST /api/flux 200 in 11196ms
 ✓ Compiled /api/store-image in 432ms (659 modules)
存储图片: https://delivery-eu4.bfl.ai/results/0b/5388743b3ad386/3b3ad38603564c9a941474636507728d/sample.png?se=2025-07-31T15%3A18%3A59Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=FFu6PKcpCuhFNiwTAZsMySpFlVe9cQ4piz5aGrXpog8%3D 分镜ID: 20277c56-6ca0-4206-89cb-f50337742dbd
图片已保存到本地: /images/shot-20277c56-6ca0-4206-89cb-f50337742dbd-1753974545028.png
 POST /api/store-image 200 in 3288ms
 ✓ Compiled in 1797ms (539 modules)
 GET /storyboards 200 in 429ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，简陋的舞蹈练习室，少女玛雅（约16岁的印度女孩，身材高挑，初具性感魅力，扎着马尾辫），华丽的黄色舞裙，孔雀羽毛元素，旋转跳舞，裙摆飞扬，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/38/555562c8239df6/c8239df6add64f67816757f824649b20/sample.png?se=2025-07-31T15%3A19%3A16Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=J4yf%2Bo0q6uGOPmolsONMbHcO0XmzrM4dhkGsCkrUKf8%3D","revised_prompt":"Medium shot, daytime, sunny, simple dance practice room, girl Maya (about 16-year-old Indian girl, tall figure, initially sexy charm, ponytail), gorgeous yellow dance dress, peacock feather elements, spinning and dancing, skirt fluttering, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753974557}
 POST /api/flux 200 in 8753ms
存储图片: https://delivery-eu4.bfl.ai/results/38/555562c8239df6/c8239df6add64f67816757f824649b20/sample.png?se=2025-07-31T15%3A19%3A16Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=J4yf%2Bo0q6uGOPmolsONMbHcO0XmzrM4dhkGsCkrUKf8%3D 分镜ID: 3ad81e3b-69c3-4bd2-a4ce-1ad489c9afb5
图片已保存到本地: /images/shot-3ad81e3b-69c3-4bd2-a4ce-1ad489c9afb5-1753974560718.png
 POST /api/store-image 200 in 2631ms
 ✓ Compiled in 697ms (539 modules)
 GET /storyboards 200 in 61ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，夜晚，舞台，灯光璀璨，背景虚化，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信），华丽的孔雀舞裙，自信的微笑，高举金色奖杯，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/63/578287133edd6b/133edd6b1eed4a67b6c6bf7f6e6cac42/sample.png?se=2025-07-31T15%3A19%3A39Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=tS37Cp7b7UWEpO90ZkD/fFHx7mMU%2BlIwMCkzg22Jqlk%3D","revised_prompt":"Mid-shot, night, stage, dazzling lights, blurred background, young Maya (about a 20-year-old Indian young woman, hot and sexy figure, stunningly beautiful appearance, long flowing hair, full of confidence), gorgeous peacock dance dress, confident smile, holding a golden trophy high, ultra HD, rich details, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753974580}
 POST /api/flux 200 in 9717ms
存储图片: https://delivery-eu1.bfl.ai/results/63/578287133edd6b/133edd6b1eed4a67b6c6bf7f6e6cac42/sample.png?se=2025-07-31T15%3A19%3A39Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=tS37Cp7b7UWEpO90ZkD/fFHx7mMU%2BlIwMCkzg22Jqlk%3D 分镜ID: 2b364dcc-8191-4e5b-bad3-226c1f7b462a
图片已保存到本地: /images/shot-2b364dcc-8191-4e5b-bad3-226c1f7b462a-1753974584130.png
 POST /api/store-image 200 in 3261ms
 ✓ Compiled in 1235ms (539 modules)
 GET /storyboards 200 in 189ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），张开双臂，普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），走过来，拥抱，卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），欣慰的泪水和笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-eu1.bfl.ai/results/c0/5972638f29b702/8f29b70270594dce9532be4df7519a3e/sample.png?se=2025-07-31T15%3A19%3A58Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=xo0FssRYR2j8l7t43or4neGm%2Bg3/I7fU9tMGpZlaE7o%3D","revised_prompt":"Medium shot, daytime, bright sunshine, outside a beautiful villa, young Maya (about 20 years old, Indian young woman, hot and sexy figure, stunningly beautiful, long flowing hair, full of confidence, wearing a gorgeous peacock dance dress), arms spread wide, Priya (about 45 years old, Indian woman, still charming, figure still sexy, but weathered face, dressed in tasteful modern clothing) walking over, embracing, Kamala (about 80 years old, Indian elderly woman, full head of white hair, wearing a clean and tidy beige sari), tears of relief and smiles, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753974599}
 POST /api/flux 200 in 8734ms
存储图片: https://delivery-eu1.bfl.ai/results/c0/5972638f29b702/8f29b70270594dce9532be4df7519a3e/sample.png?se=2025-07-31T15%3A19%3A58Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=xo0FssRYR2j8l7t43or4neGm%2Bg3/I7fU9tMGpZlaE7o%3D 分镜ID: 9a3b5d38-7673-41f9-8cd0-87fa3a1ae81e
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外，三代女人手拉着手，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着华丽的孔雀舞裙），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），微笑，走进新家，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
图片已保存到本地: /images/shot-9a3b5d38-7673-41f9-8cd0-87fa3a1ae81e-1753974603972.png
 POST /api/store-image 200 in 4523ms
FLUX_API_KEY: 存在
生成图片提示词: 中景镜头，白天，阳光明媚，新家客厅，舒适的沙发，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，已换上漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），依偎在一起，幸福的表情，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
 ✓ Compiled in 3s (539 modules)
 GET /storyboards 200 in 246ms
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/43/61363650f8d5b5/50f8d5b55efb4de480bc7f9e1d153646/sample.png?se=2025-07-31T15%3A20%3A14Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=QTAaBjfQ4LHKaw/nxJWQ7ZQucKOotTBbY2vpBxJTrkc%3D","revised_prompt":"Medium shot, daytime, sunny, outside a beautiful villa, three generations of women holding hands, young Maya (about 20-year-old Indian young woman, hot and sexy figure, stunningly beautiful appearance, long flowing hair, full of confidence, wearing a gorgeous peacock dance dress), Priya (about 45-year-old Indian woman, still charming, figure still sexy, but with a weathered face, wearing decent modern clothes), Kamala (about 80-year-old Indian elderly woman, full white hair, wearing a clean and tidy beige sari), smiling, walking into the new home, ultra HD, richly detailed, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753974618}
 POST /api/flux 200 in 16637ms
存储图片: https://delivery-us1.bfl.ai/results/43/61363650f8d5b5/50f8d5b55efb4de480bc7f9e1d153646/sample.png?se=2025-07-31T15%3A20%3A14Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=QTAaBjfQ4LHKaw/nxJWQ7ZQucKOotTBbY2vpBxJTrkc%3D 分镜ID: ec9aed47-8473-454b-8db3-2e1386e90e35
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/fa/6173601e4f3fda/1e4f3fda1054414c80d4598e235058b8/sample.png?se=2025-07-31T15%3A20%3A18Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=hv3uM6wJCNLA9IwY8sk/%2BojWb7fWgftjAOg2AawbP5I%3D","revised_prompt":"Medium shot, daytime, sunny, new home living room, comfortable sofa, young Maya (about 20-year-old Indian young woman, hot and sexy figure, stunningly beautiful appearance, long flowing hair, confident, dressed in pretty casual clothes), Priya (about 45-year-old Indian woman, still charming, figure still sexy, but weathered face, wearing neat modern clothes), Kamala (about 80-year-old Indian elderly woman, full head of white hair, wearing clean and tidy beige sari), cuddling together, happy expressions, ultra HD, rich details, cinematic texture, photo-realistic, cinematic lighting, 4K quality."}],"created":1753974619}
 POST /api/flux 200 in 13589ms
存储图片: https://delivery-us1.bfl.ai/results/fa/6173601e4f3fda/1e4f3fda1054414c80d4598e235058b8/sample.png?se=2025-07-31T15%3A20%3A18Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=hv3uM6wJCNLA9IwY8sk/%2BojWb7fWgftjAOg2AawbP5I%3D 分镜ID: 5dd5bd9f-c0d9-4b58-86a7-2d55295a6a74
图片已保存到本地: /images/shot-ec9aed47-8473-454b-8db3-2e1386e90e35-1753974620571.png
 POST /api/store-image 200 in 2202ms
图片已保存到本地: /images/shot-5dd5bd9f-c0d9-4b58-86a7-2d55295a6a74-1753974623341.png
 POST /api/store-image 200 in 3381ms
 ✓ Compiled in 4.3s (539 modules)
 GET /storyboards 200 in 75ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/1.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/1.pack.gz'
FLUX_API_KEY: 存在
生成图片提示词: 特写镜头，白天，阳光明媚，新家客厅，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着漂亮的日常便服），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着得体的现代服装），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色莎丽），紧紧挨着，灿烂的笑容，释怀的表情，幸福的笑容，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，尺寸枚举值: 9:16
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/6b/64827249842c34/49842c34542b414abc8f15e609f8eb9f/sample.png?se=2025-07-31T15%3A20%3A49Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=BsjyKAutsGxKoGD5OsM8ITKUbBCOR3VYoJrqwakUxF0%3D","revised_prompt":"Close-up shot, daytime, bright sunshine, new home living room, young Maya (about 20-year-old Indian young woman, hot and sexy figure, stunningly beautiful appearance, long flowing hair, full of confidence, wearing beautiful casual clothes), Priya (about 45-year-old Indian woman, still charming, figure still sexy, but weathered face, wearing appropriate modern clothing), Kamala (about 80-year-old Indian elderly woman, full of white hair, wearing clean and tidy beige sari), closely together, brilliant smiles, relieved expressions, happy smiles, ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753974651}
 POST /api/flux 200 in 13262ms
存储图片: https://delivery-us1.bfl.ai/results/6b/64827249842c34/49842c34542b414abc8f15e609f8eb9f/sample.png?se=2025-07-31T15%3A20%3A49Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=BsjyKAutsGxKoGD5OsM8ITKUbBCOR3VYoJrqwakUxF0%3D 分镜ID: 642149cb-d3dd-40d6-95a5-2378765d4d6a
图片已保存到本地: /images/shot-642149cb-d3dd-40d6-95a5-2378765d4d6a-1753974654169.png
 POST /api/store-image 200 in 2410ms
 ✓ Compiled in 1837ms (539 modules)
 GET /storyboards 200 in 336ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
FLUX_API_KEY: 存在
生成图片提示词: 测试图片
图片尺寸: 16:9
使用APICore flux-kontext-pro模型，尺寸枚举值: 16:9
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/10/068302c9f440e9/c9f440e91e804af99bd4e0fb824f48bf/sample.png?se=2025-07-31T15%3A27%3A49Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=TA7xk6JZ7IokMEZVabgE5t2lTNmzKpUCwFoziB3PhCo%3D","revised_prompt":"test image"}],"created":1753975070}
 POST /api/flux 200 in 7970ms
 ✓ Compiled in 7s (1195 modules)
 GET /storyboards 200 in 2272ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
FLUX_API_KEY: 存在
生成图片提示词: 测试9:16尺寸
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，比例: 9:16 实际尺寸: 576*1024
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/37/125210dab20f26/dab20f26e1034dbaaa299a9f43d9e143/sample.png?se=2025-07-31T15%3A28%3A46Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=AyTGgChZb8h%2Bm2ofxq8ehysvtY9nvx6D5%2BHNy5yUaRI%3D","revised_prompt":"Test 9:16 Size"}],"created":1753975126}
 POST /api/flux 200 in 7044ms
 ✓ Compiled in 7.2s (539 modules)
 GET /storyboards 200 in 1655ms
 ✓ Compiled in 2.5s (539 modules)
 GET /storyboards 200 in 529ms
 ✓ Compiled in 7.9s (1177 modules)
 GET /storyboards 200 in 2220ms
 ✓ Compiled in 7.1s (539 modules)
 GET /storyboards 200 in 346ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled /api/flux in 273ms (656 modules)
FLUX_API_KEY: 存在
生成图片提示词: 测试修复后的9:16尺寸
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，aspect_ratio: 9:16
📤 FLUX API 请求体: {
  "model": "flux-kontext-pro",
  "prompt": "测试修复后的9:16尺寸",
  "aspect_ratio": "9:16"
}
 GET /storyboards 200 in 1341ms
FLUX API 响应: {"data":[{"url":"https://delivery-eu4.bfl.ai/results/39/253703eadb6226/eadb622615d94388aae6081fc03dd8d9/sample.png?se=2025-07-31T15%3A30%3A54Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=FjBwru4Y7PbkYyNr46NH6pJV6HwTxtqLklHR/rVJTmg%3D","revised_prompt":"Test fixed 9:16 size"}],"created":1753975255}
 POST /api/flux 200 in 15713ms
 ✓ Compiled in 4.2s (539 modules)
 GET /storyboards 200 in 1812ms
 ✓ Compiled in 5s (539 modules)
 GET /storyboards 200 in 420ms
 ✓ Compiled in 1394ms (539 modules)
 GET /storyboards 200 in 214ms
 ✓ Compiled in 1736ms (1177 modules)
 GET /storyboards 200 in 228ms
 GET /storyboards 200 in 595ms
 ○ Compiling /api/flux ...
 ✓ Compiled /api/flux in 1160ms (656 modules)
FLUX_API_KEY: 存在
生成图片提示词: [电影感镜头，柔和的晨光，印度乡村一间破旧的土坯房门口。安嘉莉（Anjali），38岁，印度妇女，面容忧虑，穿着褪色的蓝色棉布纱丽，正在为儿子拉维（Ravi）整理领口。拉维，9岁，印度男孩，穿着整洁的白色衬衫和卡其色短裤校服，背着棕色书包。拉詹（Rajan），40岁，印度男人，身材瘦削，黑色胡须，面容严肃，穿着磨损的深棕色长袖衬衫和深色长裤，站在门内。普莉娅（Priya），8岁，印度女孩，扎马尾辫，眼神渴望，穿着破旧的黄色连衣裙式上衣和配套的旧裤子，站在一旁。背景是简陋的土坯房，细节丰富，电影质感，4K画质。]
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，aspect_ratio: 9:16
📤 FLUX API 请求体: {
  "model": "flux-kontext-pro",
  "prompt": "[电影感镜头，柔和的晨光，印度乡村一间破旧的土坯房门口。安嘉莉（Anjali），38岁，印度妇女，面容忧虑，穿着褪色的蓝色棉布纱丽，正在为儿子拉维（Ravi）整理领口。拉维，9岁，印度男孩，穿着整洁的白色衬衫和卡其色短裤校服，背着棕色书包。拉詹（Rajan），40岁，印度男人，身材瘦削，黑色胡须，面容严肃，穿着磨损的深棕色长袖衬衫和深色长裤，站在门内。普莉娅（Priya），8岁，印度女孩，扎马尾辫，眼神渴望，穿着破旧的黄色连衣裙式上衣和配套的旧裤子，站在一旁。背景是简陋的土坯房，细节丰富，电影质感，4K画质。]",
  "aspect_ratio": "9:16"
}
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/6a/480900a0c41a14/a0c41a14bccb41afb4f528974a588681/sample.png?se=2025-07-31T15%3A34%3A42Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=uTRBv4O7kvonltqMtr3bECxjLyH0LKl5TY27pNLxCus%3D","revised_prompt":"[cinematic shot, soft morning light, at the entrance of a dilapidated mud-brick house in an Indian village. Anjali, 38, Indian woman, worried expression, wearing a faded blue cotton saree, adjusting the collar for her son Ravi. Ravi, 9, Indian boy, neatly dressed in a white shirt and khaki shorts school uniform, carrying a brown backpack. Rajan, 40, Indian man, lean build, black beard, serious expression, wearing a worn dark brown long-sleeve shirt and dark trousers, standing inside the door. Priya, 8, Indian girl, with a ponytail, longing look, wearing a torn yellow dress-style top and matching old pants, standing aside. Background is a simple mud-brick house, richly detailed, cinematic texture, 4K quality.]"}],"created":1753975483}
 POST /api/flux 200 in 11081ms
 ○ Compiling /api/store-image ...
 ✓ Compiled /api/store-image in 908ms (659 modules)
存储图片: https://delivery-us1.bfl.ai/results/6a/480900a0c41a14/a0c41a14bccb41afb4f528974a588681/sample.png?se=2025-07-31T15%3A34%3A42Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=uTRBv4O7kvonltqMtr3bECxjLyH0LKl5TY27pNLxCus%3D 分镜ID: e9d6e64d-012f-4e7a-9597-17ab25b7173f
图片已保存到本地: /images/shot-e9d6e64d-012f-4e7a-9597-17ab25b7173f-1753975488336.png
 POST /api/store-image 200 in 4840ms
 ✓ Compiled in 2.1s (539 modules)
 ○ Compiling /_not-found ...
 GET /storyboards 200 in 472ms
 ✓ Compiled /_not-found in 2.1s (1188 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET /_next/static/webpack/c6fdcd862388336b.webpack.hot-update.json 404 in 2601ms
 GET /storyboards 200 in 188ms
 ○ Compiling /api/gemini ...
 ✓ Compiled /api/gemini in 1656ms (656 modules)
GEMINI_API_KEY: 存在
GEMINI_API_URL: https://openrouter.ai/api/v1/chat/completions
OpenRouter 响应: {
  "id": "gen-**********-APK2NCB0jnWJxQKl6gLE",
  "provider": "Google AI Studio",
  "model": "google/gemini-2.0-flash-001",
  "object": "chat.completion",
  "created": **********,
  "choices": [
    {
      "logprobs": null,
      "finish_reason": "stop",
      "native_finish_reason": "STOP",
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "**分镜1**\n分镜内容：在一个暴雨的夜晚，一条潮湿狭窄的印度小巷里，路面反射着昏黄的灯光。普里亚一手打着一把破旧的雨伞，一手温柔地抱着一个襁褓中的婴儿，她低头看着婴儿，眼神中充满了绝望、心碎和不舍。\n图片提示词：电影感镜头，光线昏暗，暴雨倾盆，印度小巷，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽的边缘有些磨损，但依然难掩其傲人曲线），怀抱婴儿，表情绝望心碎，背景是湿滑的石板路和昏黄的灯光，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, blue saree, rainy Indian alley, holding baby, sad expression, close-up, slow motion.\n字幕：(无对白)\n\n**分镜2**\n分镜内容：在同一条下着雨的印度小巷里，普里亚泪流满面地将怀里的婴儿递给她的母亲卡马拉。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。\n图片提示词：特写镜头，光线昏暗，暴雨倾盆，印度小巷，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽的边缘有些磨损，但依然难掩其傲人曲线），卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），普里亚递婴儿给卡马拉，普里亚泪流满面，头顶想象气泡，一枚硬币，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, blue saree, Kamala, old, brown saree, rainy alley, handing over baby, tears, close-up, tracking shot.\n字幕：(无对白)\n\n**分镜3**\n分镜内容：在雨中，卡马拉紧紧地抱着襁褓中的外孙女玛雅，用自己的身体和纱丽为她挡雨，眼神坚定，充满了保护欲。\n图片提示词：中景镜头，光线昏暗，暴雨倾盆，印度小巷，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），怀抱婴儿玛雅，眼神坚定，为婴儿挡雨，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, brown saree, rainy alley, holding baby Maya, protective expression, medium shot, slow motion.\n字幕：(无对白)\n\n**分镜4**\n分镜内容：在一个陈旧但温暖的印度室内，墙壁斑驳。卡马拉坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，她的脸上带着慈爱的微笑。\n图片提示词：电影感镜头，柔和光线，印度室内，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），坐在沙发上，用奶瓶喂婴儿玛雅，表情慈爱，背景是斑驳的墙壁和简陋的家具，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, brown saree, Indian home, feeding baby Maya, loving expression, medium shot, static shot.\n字幕：(无对白)\n\n**分镜5**\n分镜内容：几年后，在尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉背着一个装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。\n图片提示词：广角镜头，白天，阳光明媚，印度街头，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽），背着麻袋捡拾废品，表情吃力，汗流浃背，背景是杂乱的建筑和垃圾堆，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Indian street, collecting trash, tired expression, wide shot, tracking shot.\n字幕：(无对白)\n\n**分镜6**\n分镜内容：在一家明亮的印度儿童服装店里，卡马拉脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。\n图片提示词：中景镜头，白天，明亮光线，印度儿童服装店，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽），取下黄色公主裙，表情慈祥，裙子上有白色蝴蝶结，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Indian clothing store, picking yellow dress, happy expression, medium shot, close-up on dress.\n字幕：(无对白)\n\n**分镜7**\n分镜内容：回到简陋的家中，卡马拉将黄色的公主裙递给小女孩玛雅，玛雅接过裙子，脸上绽放出无比惊喜和灿烂的笑容。\n图片提示词：特写镜头，白天，柔和光线，印度室内，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，继承了母亲的绝美容貌），卡马拉递裙子给玛雅，玛雅接过裙子，表情惊喜灿烂，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Maya, little girl, Indian home, giving yellow dress, happy expression, close-up, slow motion.\n字幕：(无对白)\n\n**分镜8**\n分镜内容：在洒满阳光的房间地板上，周围放着许多编织好的小篮子。卡马拉和穿着黄色公主裙的小女孩玛雅坐在一起，卡马拉正手把手地教她编织篮子。\n图片提示词：中景镜头，白天，阳光明媚，印度室内，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，继承了母亲的绝美容貌，穿着黄色公主裙），一起编织篮子，表情专注，背景是编织好的篮子，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Maya, little girl, yellow dress, Indian home, weaving baskets, medium shot, static shot.\n字幕：(无对白)\n\n**分镜9**\n分镜内容：在一个简陋的印度乡村学校门口，卡马拉蹲下身子，为穿着黄色公主裙、背着书包的小女孩玛雅整理衣领，满眼都是骄傲和慈爱。\n图片提示词：中景镜头，白天，阳光明媚，印度乡村学校门口，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色莎丽），小女孩玛雅（约6-7岁的印度女孩，活泼可爱，大眼睛长头发，继承了母亲的绝美容貌，穿着黄色公主裙，背着书包），卡马拉为玛雅整理衣领，表情骄傲慈爱，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Maya, little girl, yellow dress, school entrance, adjusting collar, proud expression, medium shot, slow motion.\n字幕：(无对白)\n\n**分镜10**\n分镜内容：在一个有窗户的简陋舞蹈练习室里，少女玛雅正在优雅地旋转跳舞，裙摆飞扬。\n图片提示词：动态抓拍，白天，柔和光线，印度舞蹈练习室，少女玛雅（约16岁的印度女孩，身材高挑，初具性感魅力，扎着马尾辫，穿着一套充满活力的黄色舞蹈用lehenga choli（露脐上衣和长裙），展露出紧致的腰部），旋转跳舞，裙摆飞扬，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, teenager, yellow lehenga choli, dance studio, spinning, graceful movement, dynamic shot, tracking shot.\n字幕：(无对白)\n\n**分镜11**\n分镜内容：在灯光璀璨的舞台上，背景虚化。年轻的玛雅面带自信的微笑，高高举起一座金色的奖杯。\n图片提示词：特写镜头，夜晚，舞台灯光，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着一套饰有孔雀羽毛的华丽黄色lehenga choli，大胆地展示着她完美的腰部和腹部），高举奖杯，表情自信微笑，背景虚化，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow lehenga choli, stage, holding trophy, confident expression, close-up, slow motion.\n字幕：(无对白)\n\n**分镜12**\n分镜内容：在一个漂亮的别墅外，阳光明媚。年轻的玛雅张开双臂，激动地拥抱着向她走来的母亲普里亚，一旁的卡马拉看着她们，脸上露出欣慰的泪水和笑容。\n图片提示词：中景镜头，白天，阳光明媚，别墅外，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着一套饰有孔雀羽毛的华丽黄色lehenga choli，大胆地展示着她完美的腰部和腹部），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着一件优雅的、剪裁合身的祖母绿现代连衣裙），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边），玛雅拥抱普里亚，卡马拉欣慰落泪，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow lehenga choli, Priya, middle-aged, green dress, Kamala, old woman, beige saree, villa exterior, embracing, happy tears, medium shot, static shot.\n字幕：(无对白)\n\n**分镜13**\n分镜内容：三代女人手拉着手，微笑着一起走进她们的漂亮新家。走在中间的年轻的玛雅，左手牵着她的母亲普里亚，右手牵着她的外祖母卡马拉。\n图片提示词：电影感长镜头，白天，阳光明媚，别墅外，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着一套饰有孔雀羽毛的华丽黄色lehenga choli，大胆地展示着她完美的腰部和腹部），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着一件优雅的、剪裁合身的祖母绿现代连衣裙），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边），手拉手走进新家，表情微笑，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, yellow lehenga choli, Priya, middle-aged, green dress, Kamala, old woman, beige saree, walking into villa, holding hands, happy expressions, long shot, tracking shot.\n字幕：(无对白)\n\n**分镜14**\n分镜内容：在新家舒适的客厅沙发上，三代女人幸福地依偎在一起。年轻的玛雅坐在中间，一边是她的母亲普里亚，另一边是她的外祖母卡马拉。\n图片提示词：中景镜头，白天，柔和光线，别墅客厅，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着一件优雅的、剪裁合身的祖母绿现代连衣裙），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边），依偎在沙发上，表情幸福，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, silk top, Priya, middle-aged, green dress, Kamala, old woman, beige saree, villa living room, cuddling on sofa, happy expressions, medium shot, static shot.\n字幕：(无对白)\n\n**分镜15**\n分镜内容：一张幸福的三代人合影特写。年轻的玛雅、她的母亲普里亚和她的外祖母卡马拉紧紧挨着，都对着镜头露出灿烂、释怀而幸福的笑容，画面其乐融融，充满了希望。\n图片提示词：特写镜头，白天，柔和光线，别墅客厅，年轻的玛雅（约20岁的印度年轻女性，身材火辣性感，容貌绝美，长发飘逸，充满自信，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形），普里亚（约45岁的印度女人，风韵犹存，身材依然性感，但面带风霜，穿着一件优雅的、剪裁合身的祖母绿现代连衣裙），卡马拉（约80岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边），三代人合影，表情灿烂幸福，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young woman, silk top, Priya, middle-aged, green dress, Kamala, old woman, beige saree, villa living room, family portrait, happy smiles, close-up, static shot.\n字幕：(无对白)\n",
        "refusal": null,
        "reasoning": null
      }
    }
  ],
  "usage": {
    "prompt_tokens": 3303,
    "completion_tokens": 3513,
    "total_tokens": 6816
  }
}
 POST /api/gemini 200 in 26540ms
GEMINI_API_KEY: 存在
GEMINI_API_URL: https://openrouter.ai/api/v1/chat/completions
OpenRouter 响应: {
  "id": "gen-**********-KbNzEzbvpuQI9O44dkFa",
  "provider": "Google AI Studio",
  "model": "google/gemini-2.0-flash-001",
  "object": "chat.completion",
  "created": **********,
  "choices": [
    {
      "logprobs": null,
      "finish_reason": "content_filter",
      "native_finish_reason": "PROHIBITED_CONTENT",
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "**分镜1**\n分镜内容：在一个暴雨的夜晚，一条潮湿狭窄的印度小巷里，路面反射着昏黄的灯光。普里亚一手打着一把破旧的雨伞，一手温柔地抱着一个襁褓中的婴儿，她低头看着婴儿，眼神中充满了绝望、心碎和不舍。\n图片提示词：电影感镜头，光线昏暗，暴雨倾盆，印度小巷，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽的边缘有些磨损，但依然难掩其傲人曲线），怀抱婴儿，表情绝望心碎，背景是湿滑的石板路和昏黄的灯光，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, blue saree, rainy Indian alley, holding baby, sad expression, close-up, slow motion.\n字幕：(无对白) \n\n\n**分镜2**\n分镜内容：在同一条下着雨的印度小巷里，普里亚泪流满面地将怀里的婴儿递给她的母亲卡马拉。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。\n图片提示词：特写镜头，光线昏暗，暴雨倾盆，印度小巷，普里亚（约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽的边缘有些磨损，但依然难掩其傲人曲线），卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），普里亚递婴儿给卡马拉，普里亚泪流满面，头顶想象气泡，一枚硬币，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy, blue saree, Kamala, old, brown saree, rainy alley, handing over baby, tears, close-up, tracking shot.\n字幕：(无对白) \n\n\n**分镜3**\n分镜内容：在雨中，卡马拉紧紧地抱着襁褓中的外孙女玛雅，用自己的身体和纱丽为她挡雨，眼神坚定，充满了保护欲。\n图片提示词：中景镜头，光线昏暗，暴雨倾盆，印度小巷，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），怀抱婴儿玛雅，眼神坚定，为婴儿挡雨，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, brown saree, rainy alley, holding baby Maya, protective expression, medium shot, slow motion.\n字幕：(无对白) \n\n\n**分镜4**\n分镜内容：在一个陈旧但温暖的印度室内，墙壁斑驳。卡马拉坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，她的脸上带着慈爱的微笑。\n图片提示词：电影感镜头，柔和光线，印度室内，卡马拉（约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽），坐在沙发上，用奶瓶喂婴儿玛雅，表情慈爱，背景是斑驳的墙壁和简陋的家具，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, brown saree, Indian home, feeding baby Maya, loving expression, medium shot, static shot.\n字幕：(无对白) \n\n**分镜5**\n分镜内容：十年后，在一间简陋的教室里，阳光透过窗户洒进来。十岁的玛雅穿着朴素的校服，认真地听着老师讲课，眼神中充满了求知欲。\n图片提示词：中景镜头，白天，柔和光线，印度乡村小学教室，玛雅（约10岁的印度女孩，穿着朴素的校服，干净整洁，大眼睛，短发），认真听课，眼神充满求知欲，背景是简陋的教室和黑板，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, 10 years old, school uniform, classroom, listening to teacher, curious expression, medium shot, static shot.\n字幕：(无对白) \n\n**分镜6**\n分镜内容：几年后，在尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉背着一个装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。\n图片提示词：广角镜头，白天，阳光明媚，印度街头，卡马拉（约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽），背着麻袋捡拾废品，表情吃力，汗流浃背，背景是杂乱的建筑和垃圾堆，超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old, worn saree, Indian street, collecting trash, tired expression, wide shot, tracking shot.\n字幕：(无对白) \n\n\n**分镜7**\n分镜内容：在一家明亮的印度儿童服装店里，卡马拉脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。\n图片提示词：中景镜头，白天，明",
        "refusal": null,
        "reasoning": null
      }
    }
  ],
  "usage": {
    "prompt_tokens": 3365,
    "completion_tokens": 1343,
    "total_tokens": 4708
  }
}
 POST /api/gemini 200 in 8716ms
GEMINI_API_KEY: 存在
GEMINI_API_URL: https://openrouter.ai/api/v1/chat/completions
OpenRouter 响应: {
  "id": "gen-**********-YePlmo0DyijFoldNDDFg",
  "provider": "Google AI Studio",
  "model": "google/gemini-2.0-flash-001",
  "object": "chat.completion",
  "created": **********,
  "choices": [
    {
      "logprobs": null,
      "finish_reason": "stop",
      "native_finish_reason": "STOP",
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "**分镜1**\n分镜内容：暴雨之夜，普里亚站在潮湿狭窄的印度小巷里，路面反射着昏黄的灯光，她一手打着破旧的雨伞，一手温柔地抱着襁褓中的婴儿，低头看着婴儿，眼神绝望、心碎、不舍。\n图片提示词：电影感镜头，光线昏暗，暴雨之夜，潮湿狭窄的印度小巷，路面反射着昏黄的灯光。普里亚，约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽边缘磨损，一手打着破旧的雨伞，一手温柔地抱着襁褓中的婴儿，低头看着婴儿，眼神绝望、心碎、不舍。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy Indian woman, blue sari, rainy alley, holding baby, sad expression, close-up, slow motion.\n字幕：[无]\n\n**分镜2**\n分镜内容：同一条小巷，普里亚泪流满面地将婴儿递给她的母亲卡马拉。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。\n图片提示词：电影感镜头，光线昏暗，暴雨之夜，潮湿狭窄的印度小巷。普里亚，约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着深蓝色旧纱丽，泪流满面地将怀里的婴儿递给卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Priya, sexy Indian woman, blue sari, handing baby to Kamala, old Indian woman, brown sari, rainy alley, sad expression, close-up.\n字幕：[无]\n\n**分镜3**\n分镜内容：雨中，卡马拉紧紧抱着襁褓中的外孙女玛雅，用自己的身体和纱丽为她挡雨，眼神坚定，充满保护欲。\n图片提示词：特写镜头，光线昏暗，暴雨之夜。卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽，紧紧地抱着襁褓中的外孙女玛雅，襁褓中的印度女婴，用自己的身体和纱丽为她挡雨，眼神坚定，充满保护欲。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, holding baby Maya, rainy night, protective expression, close-up.\n字幕：[无]\n\n**分镜4**\n分镜内容：陈旧但温暖的印度室内，卡马拉坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，脸上带着慈爱的微笑。\n图片提示词：中景镜头，柔和的室内光线，陈旧但温暖的印度室内，墙壁斑驳。卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽，坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，襁褓中的印度女婴，脸上带着慈爱的微笑。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, feeding baby Maya, interior, loving expression, medium shot.\n字幕：[无]\n\n**分镜5**\n分镜内容：几年后，尘土飞扬的印度街头，卡马拉背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。\n图片提示词：广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, collecting trash, dusty street, tired expression, wide shot.\n字幕：[无]\n\n**分镜6**\n分镜内容：明亮的印度儿童服装店里，卡马拉脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。\n图片提示词：中景镜头，明亮的光线，印度儿童服装店。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, buying yellow dress, clothing store, happy expression, medium shot.\n字幕：[无]\n\n**分镜7**\n分镜内容：回到简陋的家中，卡马拉将黄色的公主裙递给小女孩玛雅，玛雅接过裙子，脸上绽放出无比惊喜和灿烂的笑容。\n图片提示词：中景镜头，柔和的室内光线，简陋的家中。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，将黄色的公主裙递给小女孩玛雅，约6-7岁的印度女孩，活泼可爱，大眼睛长头发，接过裙子，脸上绽放出无比惊喜和灿烂的笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, giving yellow dress to Maya, young Indian girl, happy expression, medium shot.\n字幕：[无]\n\n**分镜8**\n分镜内容：简陋的印度乡村学校门口，卡马拉蹲下身子，为穿着黄色公主裙、背着书包的小女孩玛雅整理衣领，满眼都是骄傲和慈爱。\n图片提示词：中景镜头，白天，阳光明媚，简陋的印度乡村学校门口。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，蹲下身子，为穿着黄色公主裙、背着书包的小女孩玛雅，约6-7岁的印度女孩，整理衣领，满眼都是骄傲和慈爱。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Kamala, old Indian woman, brown sari, adjusting Maya's dress, school entrance, proud expression, medium shot.\n字幕：[无]\n\n**分镜9**\n分镜内容：她们简陋的家中，小女孩玛雅正坐在一张旧木桌前，借着一盏昏黄的台灯光芒认真读书，眼神专注。卡马拉在不远处慈爱地看着她，脸上带着疲惫但满足的微笑。\n图片提示词：中景镜头，光线昏暗，夜晚，简陋的家中。小女孩玛雅，约10岁的印度女孩，面容清秀，长发编成辫子，穿着朴素但干净的校服，正坐在一张旧木桌前，借着一盏昏黄的台灯光芒认真读书，眼神专注。卡马拉，约69岁的印度老妇人，面容苍老，身穿更破旧的土褐色纱丽，在不远处慈爱地看着她，脸上带着疲惫但满足的微笑。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian girl, school uniform, studying, Kamala, old Indian woman, brown sari, watching her, interior, medium shot.\n字幕：[无]\n\n**分镜10**\n分镜内容：有窗户的简陋舞蹈练习室里，少女玛雅正在优雅地旋转跳舞，裙摆飞扬。\n图片提示词：中景镜头，白天，明亮的光线，有窗户的简陋舞蹈练习室。少女玛雅，约16岁的印度女孩，身材高挑，扎着马尾辫，穿着一套充满活力的黄色舞蹈用lehenga choli，展露出紧致的腰部，正在优雅地旋转跳舞，裙摆飞扬。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, teenage Indian girl, yellow lehenga choli, dancing, dance studio, graceful movement, medium shot.\n字幕：[无]\n\n**分镜11**\n分镜内容：大学毕业典礼现场，年轻的玛雅一手高举毕业证书，一手将学士帽抛向空中，脸上是灿烂自信的笑容。她的外祖母卡马拉站在人群中，激动地鼓掌，热泪盈眶。\n图片提示词：广角镜头，白天，阳光明媚，大学毕业典礼现场。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，充满自信，穿着黑色的学士服和学士帽，学士服敞开，露出里面一件性感的白色紧身短裙，一手高举毕业证书，一手将学士帽抛向空中，脸上是灿烂自信的笑容。卡马拉，约82岁的印度老妇人，满头白发，穿着一生中最好的干净棉纱丽，站在人群中，激动地鼓掌，热泪盈眶。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, graduation gown, throwing hat, Kamala, old Indian woman, sari, clapping, graduation ceremony, wide shot.\n字幕：[无]\n\n**分镜12**\n分镜内容：灯光璀璨的舞台上，年轻的玛雅面带自信的微笑，高高举起一座金色的奖杯。\n图片提示词：特写镜头，夜晚，灯光璀璨的舞台，背景虚化。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，充满自信，穿着一套饰有孔雀羽毛的华丽黄色lehenga choli，大胆地展示着她完美的腰部和腹部，面带自信的微笑，高高举起一座金色的奖杯。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, yellow lehenga choli, holding trophy, stage, confident expression, close-up.\n字幕：[无]\n\n**分镜13**\n分镜内容：漂亮的别墅外，阳光明媚。年轻的玛雅张开双臂，激动地拥抱着向她走来的母亲普里亚，一旁的卡马拉看着她们，脸上露出欣慰的泪水和笑容。\n图片提示词：中景镜头，白天，阳光明媚，漂亮的别墅外。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着华丽黄色lehenga choli，张开双臂，激动地拥抱着向她走来的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙。卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，看着她们，脸上露出欣慰的泪水和笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, yellow lehenga choli, hugging Priya, Indian woman, green dress, Kamala, old Indian woman, sari, happy reunion, medium shot.\n字幕：[无]\n\n**分镜14**\n分镜内容：三代女人手拉着手，微笑着一起走进她们的漂亮新家。\n图片提示词：中景镜头，白天，阳光明媚，漂亮的别墅外。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着华丽黄色lehenga choli，左手牵着她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，右手牵着她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，三代女人手拉着手，微笑着一起走进她们的漂亮新家。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, yellow lehenga choli, Priya, Indian woman, green dress, Kamala, old Indian woman, sari, walking into new home, happy family, medium shot.\n字幕：[无]\n\n**分镜15**\n分镜内容：新家舒适的客厅沙发上，三代女人幸福地依偎在一起。\n图片提示词：中景镜头，白天，明亮的光线，新家舒适的客厅沙发上。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形，坐在中间，一边是她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，另一边是她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边。三代女人幸福地依偎在一起。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, silk top and pants, Priya, Indian woman, green dress, Kamala, old Indian woman, sari, sitting on sofa, happy family, medium shot.\n字幕：[无]\n\n**分镜16**\n分镜内容：一张幸福的三代人合影特写。\n图片提示词：特写镜头，白天，明亮的光线。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形，她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，和她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，紧紧挨着，都对着镜头露出灿烂、释怀而幸福的笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, silk top and pants, Priya, Indian woman, green dress, Kamala, old Indian woman, sari, smiling, happy family, close-up.\n字幕：[无]\n\n**分镜17**\n分镜内容：在新家宽敞明亮的客厅里，年轻的玛雅正将自己获得的舞蹈奖杯和大学毕业证书并排放在一个柜子上，她的母亲普里亚和外祖母卡马拉在一旁欣慰地看着，眼中满是骄傲。\n图片提示词：中景镜头，白天，明亮的光线，新家宽敞明亮的客厅里。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，正将自己获得的舞蹈奖杯和大学毕业证书并排放在一个柜子上，她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，和外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，在一旁欣慰地看着，眼中满是骄傲。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。\n视频提示词：Maya, young Indian woman, silk top and pants, placing trophy and diploma, Priya, Indian woman, green dress, Kamala, old Indian woman, sari, watching her, proud family, medium shot.\n字幕：[无]\n",
        "refusal": null,
        "reasoning": null
      }
    }
  ],
  "usage": {
    "prompt_tokens": 2873,
    "completion_tokens": 3858,
    "total_tokens": 6731
  }
}
 POST /api/gemini 200 in 24251ms
 ✓ Compiled /api/doubao in 365ms (656 modules)
ARK_API_KEY: 存在
豆包生成图片提示词: 电影感镜头，光线昏暗，暴雨之夜，潮湿狭窄的印度小巷，路面反射着昏黄的灯光。普里亚，约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着一件薄薄的、紧贴身体的深蓝色旧纱丽，纱丽边缘磨损，一手打着破旧的雨伞，一手温柔地抱着襁褓中的婴儿，低头看着婴儿，眼神绝望、心碎、不舍。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753975995,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539759926242dd0dd99c228a5c584388c2ba5878758554bef.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153315Z&X-Tos-Expires=86400&X-Tos-Signature=144293611bd51a3f243b5de42d811bfaaa20b9330057f972b0aa16a15f44883b&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3922ms
 ✓ Compiled /api/store-image in 103ms (659 modules)
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539759926242dd0dd99c228a5c584388c2ba5878758554bef.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153315Z&X-Tos-Expires=86400&X-Tos-Signature=144293611bd51a3f243b5de42d811bfaaa20b9330057f972b0aa16a15f44883b&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 78ae7915-5ba8-49b5-b84b-debcc3f48646
图片已保存到本地: /images/shot-78ae7915-5ba8-49b5-b84b-debcc3f48646-1753975996403.jpg
 POST /api/store-image 200 in 633ms
 ✓ Compiled in 482ms (539 modules)
 GET /storyboards 200 in 158ms
ARK_API_KEY: 存在
豆包生成图片提示词: 电影感镜头，光线昏暗，暴雨之夜，潮湿狭窄的印度小巷。普里亚，约25岁的印度女人，身材火辣性感，容貌绝美，长卷发，穿着深蓝色旧纱丽，泪流满面地将怀里的婴儿递给卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽。普里亚头顶上方出现一个想象气泡，里面只有一枚孤零零的硬币。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976002,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539759997372dd0dd99c228a5c584388c2ba58787589c83a3.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153322Z&X-Tos-Expires=86400&X-Tos-Signature=4aef05092bc0c82b807c798a4413324253a721000f86fb6f1ec3aeb23455ddb8&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3334ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539759997372dd0dd99c228a5c584388c2ba58787589c83a3.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153322Z&X-Tos-Expires=86400&X-Tos-Signature=4aef05092bc0c82b807c798a4413324253a721000f86fb6f1ec3aeb23455ddb8&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 522d7582-0a0e-4dad-a449-d1c695a7c283
图片已保存到本地: /images/shot-522d7582-0a0e-4dad-a449-d1c695a7c283-1753976003583.jpg
 POST /api/store-image 200 in 569ms
 ✓ Compiled in 336ms (539 modules)
 GET /storyboards 200 in 32ms
ARK_API_KEY: 存在
豆包生成图片提示词: 特写镜头，光线昏暗，暴雨之夜。卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽，紧紧地抱着襁褓中的外孙女玛雅，襁褓中的印度女婴，用自己的身体和纱丽为她挡雨，眼神坚定，充满保护欲。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976010,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976007404bcd8900efa6e36f2bb2410c1e1430a2685a644.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153330Z&X-Tos-Expires=86400&X-Tos-Signature=4a64e84a819467519f0bdd2255ff05bd5609c7a25d7705bfa25b5bc67e5cb3fd&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3842ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976007404bcd8900efa6e36f2bb2410c1e1430a2685a644.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153330Z&X-Tos-Expires=86400&X-Tos-Signature=4a64e84a819467519f0bdd2255ff05bd5609c7a25d7705bfa25b5bc67e5cb3fd&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 31eef77d-e77e-4b2c-8b4c-5cea2f19680d
图片已保存到本地: /images/shot-31eef77d-e77e-4b2c-8b4c-5cea2f19680d-1753976011436.jpg
 POST /api/store-image 200 in 544ms
 ✓ Compiled in 292ms (539 modules)
 GET /storyboards 200 in 31ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，柔和的室内光线，陈旧但温暖的印度室内，墙壁斑驳。卡马拉，约60岁的印度老妇人，面容慈祥但布满皱纹，身穿朴素褪色的土褐色纱丽，坐在破旧的沙发上，用奶瓶耐心地喂养着怀里的玛雅，襁褓中的印度女婴，脸上带着慈爱的微笑。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976018,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397601540134bb22c3e16514339a85fa5623e3c8023cf5d8.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153338Z&X-Tos-Expires=86400&X-Tos-Signature=b41dc28646c036f706745735272db8ed66c4a136b9fd8fa5ee540293b51da438&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3515ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397601540134bb22c3e16514339a85fa5623e3c8023cf5d8.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153338Z&X-Tos-Expires=86400&X-Tos-Signature=b41dc28646c036f706745735272db8ed66c4a136b9fd8fa5ee540293b51da438&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: fe5109c3-d8af-4044-a32a-4455dd044d78
图片已保存到本地: /images/shot-fe5109c3-d8af-4044-a32a-4455dd044d78-1753976019087.jpg
 POST /api/store-image 200 in 541ms
 ✓ Compiled in 211ms (539 modules)
 GET /storyboards 200 in 36ms
ARK_API_KEY: 存在
豆包生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976022,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 02175397602215634bb22c3e16514339a85fa5623e3c802a1225f"}}
豆包 API error: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976022,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 02175397602215634bb22c3e16514339a85fa5623e3c802a1225f"}}
 POST /api/doubao 500 in 186ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，明亮的光线，印度儿童服装店。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，脸上带着慈祥的微笑，小心翼翼地从衣架上取下一件漂亮的柠檬黄公主裙，裙子上有个白色的蝴蝶结。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976026,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397602390634bb22c3e16514339a85fa5623e3c802691e4c.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153346Z&X-Tos-Expires=86400&X-Tos-Signature=2950d6c7d2917e55b2a637086123a76d838cf65c89469df4ccef82c4ee3a1c5e&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3127ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397602390634bb22c3e16514339a85fa5623e3c802691e4c.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153346Z&X-Tos-Expires=86400&X-Tos-Signature=2950d6c7d2917e55b2a637086123a76d838cf65c89469df4ccef82c4ee3a1c5e&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: d5c43269-a099-4310-a5df-96a158cb577b
图片已保存到本地: /images/shot-d5c43269-a099-4310-a5df-96a158cb577b-1753976027507.jpg
 POST /api/store-image 200 in 550ms
 ✓ Compiled in 361ms (539 modules)
 GET /storyboards 200 in 27ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，柔和的室内光线，简陋的家中。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，将黄色的公主裙递给小女孩玛雅，约6-7岁的印度女孩，活泼可爱，大眼睛长头发，接过裙子，脸上绽放出无比惊喜和灿烂的笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976033,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397603054434bb22c3e16514339a85fa5623e3c802a6a720.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153353Z&X-Tos-Expires=86400&X-Tos-Signature=d9cf778a09855b9c71aec1c1f7092f3e64fdf2413f3034f37599fa83e7465879&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3494ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397603054434bb22c3e16514339a85fa5623e3c802a6a720.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153353Z&X-Tos-Expires=86400&X-Tos-Signature=d9cf778a09855b9c71aec1c1f7092f3e64fdf2413f3034f37599fa83e7465879&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 9ec2532d-08f8-4956-8bb9-e1139b74de3b
图片已保存到本地: /images/shot-9ec2532d-08f8-4956-8bb9-e1139b74de3b-1753976034535.jpg
 POST /api/store-image 200 in 555ms
 ✓ Compiled in 191ms (539 modules)
 GET /storyboards 200 in 22ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，阳光明媚，简陋的印度乡村学校门口。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，蹲下身子，为穿着黄色公主裙、背着书包的小女孩玛雅，约6-7岁的印度女孩，整理衣领，满眼都是骄傲和慈爱。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976040,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397603703734bb22c3e16514339a85fa5623e3c80269bff9.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153400Z&X-Tos-Expires=86400&X-Tos-Signature=c5be098c97dd28c9a01912040fb00fc19d4e754e6856b8f6d4cd38adbec988e1&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3311ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397603703734bb22c3e16514339a85fa5623e3c80269bff9.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153400Z&X-Tos-Expires=86400&X-Tos-Signature=c5be098c97dd28c9a01912040fb00fc19d4e754e6856b8f6d4cd38adbec988e1&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: ea15facf-252b-4b7d-9050-d6007b8d39e9
图片已保存到本地: /images/shot-ea15facf-252b-4b7d-9050-d6007b8d39e9-1753976040847.jpg
 POST /api/store-image 200 in 551ms
 ✓ Compiled in 382ms (539 modules)
 GET /storyboards 200 in 40ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，光线昏暗，夜晚，简陋的家中。小女孩玛雅，约10岁的印度女孩，面容清秀，长发编成辫子，穿着朴素但干净的校服，正坐在一张旧木桌前，借着一盏昏黄的台灯光芒认真读书，眼神专注。卡马拉，约69岁的印度老妇人，面容苍老，身穿更破旧的土褐色纱丽，在不远处慈爱地看着她，脸上带着疲惫但满足的微笑。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976047,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397604422334bb22c3e16514339a85fa5623e3c80250b923.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153407Z&X-Tos-Expires=86400&X-Tos-Signature=458d0364ea1a5839c298271c5a11b50ca691e44ea34d1dc6d429d7a6c72f0dd6&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3612ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397604422334bb22c3e16514339a85fa5623e3c80250b923.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153407Z&X-Tos-Expires=86400&X-Tos-Signature=458d0364ea1a5839c298271c5a11b50ca691e44ea34d1dc6d429d7a6c72f0dd6&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 9cb3576f-e05a-4de5-a044-7d0c2a798c1d
图片已保存到本地: /images/shot-9cb3576f-e05a-4de5-a044-7d0c2a798c1d-1753976048273.jpg
 POST /api/store-image 200 in 498ms
 ✓ Compiled in 557ms (539 modules)
 ⨯ SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>) {
  page: '/storyboards'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 2.9s (1513 modules)
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，明亮的光线，有窗户的简陋舞蹈练习室。少女玛雅，约16岁的印度女孩，身材高挑，扎着马尾辫，穿着一套充满活力的黄色舞蹈用lehenga choli，展露出紧致的腰部，正在优雅地旋转跳舞，裙摆飞扬。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
 GET /storyboards 500 in 4115ms
 GET /storyboards 200 in 119ms
 GET /storyboards 200 in 65ms
 POST /api/doubao 200 in 2631ms
 GET /storyboards 200 in 535ms
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976056,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976053022277b96ce6a9666646d7f6f10a402bb53ee1cb2.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153416Z&X-Tos-Expires=86400&X-Tos-Signature=a0e50f5186afb503d40dc3bfc1765cdded3f4c17d948b17566cd4c214e195730&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 ✓ Compiled /api/flux in 407ms (748 modules)
FLUX_API_KEY: 存在
生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用APICore flux-kontext-pro模型，aspect_ratio: 9:16
📤 FLUX API 请求体: {
  "model": "flux-kontext-pro",
  "prompt": "广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。",
  "aspect_ratio": "9:16"
}
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，明亮的光线，有窗户的简陋舞蹈练习室。少女玛雅，约16岁的印度女孩，身材高挑，扎着马尾辫，穿着一套充满活力的黄色舞蹈用lehenga choli，展露出紧致的腰部，正在优雅地旋转跳舞，裙摆飞扬。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
FLUX API 响应: {"data":[{"url":"https://delivery-us1.bfl.ai/results/9a/0812889acb9724/9acb9724f1ab4549b52e6b2492f2ae3f/sample.png?se=2025-07-31T15%3A44%3A42Z\u0026sp=r\u0026sv=2024-11-04\u0026sr=b\u0026rsct=image/png\u0026sig=f5sowDp33hiup4o/2e2KMo88YMD1dBTPIab1oeRQrXw%3D","revised_prompt":"Wide-angle lens, daytime, bright sunshine, dusty Indian street, background of chaotic buildings. Kamala, about 65-year-old Indian elderly woman, face more aged, wearing a more worn-out earth-brown sari, carrying a huge sack filled with plastic bottles on her back, laboriously picking up recyclables in a pile of garbage, sweat soaking her forehead. Ultra HD, rich in detail, cinematic texture, photo-realistic, cinematic lighting effects, 4K quality."}],"created":1753976085}
 POST /api/flux 200 in 11721ms
存储图片: https://delivery-us1.bfl.ai/results/9a/0812889acb9724/9acb9724f1ab4549b52e6b2492f2ae3f/sample.png?se=2025-07-31T15%3A44%3A42Z&sp=r&sv=2024-11-04&sr=b&rsct=image/png&sig=f5sowDp33hiup4o/2e2KMo88YMD1dBTPIab1oeRQrXw%3D 分镜ID: 242a4635-fc30-4a7e-ba87-45ddc4567a84
ARK_API_KEY: 存在
豆包生成图片提示词: 广角镜头，白天，阳光明媚，大学毕业典礼现场。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，充满自信，穿着黑色的学士服和学士帽，学士服敞开，露出里面一件性感的白色紧身短裙，一手高举毕业证书，一手将学士帽抛向空中，脸上是灿烂自信的笑容。卡马拉，约82岁的印度老妇人，满头白发，穿着一生中最好的干净棉纱丽，站在人群中，激动地鼓掌，热泪盈眶。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
图片已保存到本地: /images/shot-242a4635-fc30-4a7e-ba87-45ddc4567a84-1753976087755.png
 POST /api/store-image 200 in 2550ms
 ✓ Compiled in 335ms (781 modules)
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976088,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760850634ef961016ad51c6e7b26c7ae29c8126e9eae12.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153448Z&X-Tos-Expires=86400&X-Tos-Signature=c34388d87f26de765840efd948cb3c5189da5d8026033f3ab0bc82ca19a11266&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3507ms
 GET /storyboards 200 in 96ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760850634ef961016ad51c6e7b26c7ae29c8126e9eae12.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153448Z&X-Tos-Expires=86400&X-Tos-Signature=c34388d87f26de765840efd948cb3c5189da5d8026033f3ab0bc82ca19a11266&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 83f35ceb-b07c-4197-8f19-62e141043a94
图片已保存到本地: /images/shot-83f35ceb-b07c-4197-8f19-62e141043a94-1753976088753.jpg
 POST /api/store-image 200 in 554ms
ARK_API_KEY: 存在
豆包生成图片提示词: 特写镜头，夜晚，灯光璀璨的舞台，背景虚化。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，充满自信，穿着一套饰有孔雀羽毛的华丽黄色lehenga choli，大胆地展示着她完美的腰部和腹部，面带自信的微笑，高高举起一座金色的奖杯。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
 ✓ Compiled in 463ms (781 modules)
 GET /storyboards 200 in 63ms
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976090,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760872794c498ab3b0599c760f5c1a2da52e01b093269e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153450Z&X-Tos-Expires=86400&X-Tos-Signature=458826ef4db72e57b8965b454a57b65d6b31c0587d49e84ec4757ee921604d5a&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3961ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760872794c498ab3b0599c760f5c1a2da52e01b093269e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153450Z&X-Tos-Expires=86400&X-Tos-Signature=458826ef4db72e57b8965b454a57b65d6b31c0587d49e84ec4757ee921604d5a&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 948c076d-e13c-4041-aed7-6dbdffbe5f06
图片已保存到本地: /images/shot-948c076d-e13c-4041-aed7-6dbdffbe5f06-1753976091400.jpg
 POST /api/store-image 200 in 432ms
 ✓ Compiled in 319ms (781 modules)
 GET /storyboards 200 in 28ms
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976092,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760891764ef961016ad51c6e7b26c7ae29c8126ee02516.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153452Z&X-Tos-Expires=86400&X-Tos-Signature=0e32eb629bec41e44d599b7ee1db661e4f128a30289dc456515f6cf4f91a1e47&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3127ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539760891764ef961016ad51c6e7b26c7ae29c8126ee02516.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153452Z&X-Tos-Expires=86400&X-Tos-Signature=0e32eb629bec41e44d599b7ee1db661e4f128a30289dc456515f6cf4f91a1e47&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: fbd33784-344e-4a8a-b205-977481089acb
图片已保存到本地: /images/shot-fbd33784-344e-4a8a-b205-977481089acb-1753976092616.jpg
 POST /api/store-image 200 in 385ms
 ✓ Compiled in 301ms (781 modules)
 GET /storyboards 200 in 28ms
ARK_API_KEY: 存在
豆包生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976096,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 0217539760962434ef961016ad51c6e7b26c7ae29c8126ef81d1d"}}
豆包 API error: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976096,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 0217539760962434ef961016ad51c6e7b26c7ae29c8126ef81d1d"}}
 POST /api/doubao 500 in 550ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着华丽黄色lehenga choli，张开双臂，激动地拥抱着向她走来的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙。卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，看着她们，脸上露出欣慰的泪水和笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，阳光明媚，漂亮的别墅外。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着华丽黄色lehenga choli，左手牵着她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，右手牵着她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，三代女人手拉着手，微笑着一起走进她们的漂亮新家。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976115,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761120422dd0dd99c228a5c584388c2ba5878758523e1e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153515Z&X-Tos-Expires=86400&X-Tos-Signature=6891a208899b2648c74b2823d4d10f0ec8d41cdf2f59a7782b03ad902a7d9c0b&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 4203ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761120422dd0dd99c228a5c584388c2ba5878758523e1e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153515Z&X-Tos-Expires=86400&X-Tos-Signature=6891a208899b2648c74b2823d4d10f0ec8d41cdf2f59a7782b03ad902a7d9c0b&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 8e0d7ff8-882e-462f-9562-ec5c3e7f8568
图片已保存到本地: /images/shot-8e0d7ff8-882e-462f-9562-ec5c3e7f8568-1753976116411.jpg
 POST /api/store-image 200 in 537ms
 ✓ Compiled in 350ms (780 modules)
 GET /storyboards 200 in 60ms
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976117,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976114553da2c5612ab5ef9594e1e8953b8339fc7973d5e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153517Z&X-Tos-Expires=86400&X-Tos-Signature=f20422a01d6b55383eb1f9aefe46c3e80600bae026484db17d2f43fd65d2f091&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3668ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976114553da2c5612ab5ef9594e1e8953b8339fc7973d5e.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153517Z&X-Tos-Expires=86400&X-Tos-Signature=f20422a01d6b55383eb1f9aefe46c3e80600bae026484db17d2f43fd65d2f091&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: d4394214-a73e-4b35-9ec0-6b9752ca2a3b
图片已保存到本地: /images/shot-d4394214-a73e-4b35-9ec0-6b9752ca2a3b-1753976118380.jpg
 POST /api/store-image 200 in 414ms
 ✓ Compiled in 303ms (780 modules)
 GET /storyboards 200 in 41ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，明亮的光线，新家舒适的客厅沙发上。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形，坐在中间，一边是她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，另一边是她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边。三代女人幸福地依偎在一起。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976127,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761247248821430536922c5641a037fa3b90f8efd3af46.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153527Z&X-Tos-Expires=86400&X-Tos-Signature=0699e03f754970f397b80173b4116eab30d64b5b280d8aa48eb28ee5ae356081&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3566ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761247248821430536922c5641a037fa3b90f8efd3af46.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153527Z&X-Tos-Expires=86400&X-Tos-Signature=0699e03f754970f397b80173b4116eab30d64b5b280d8aa48eb28ee5ae356081&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: ca5f3d90-950b-46e7-84d7-b62b66e8fa27
图片已保存到本地: /images/shot-ca5f3d90-950b-46e7-84d7-b62b66e8fa27-1753976128508.jpg
 POST /api/store-image 200 in 585ms
 ✓ Compiled in 694ms (780 modules)
 GET /storyboards 200 in 55ms
ARK_API_KEY: 存在
豆包生成图片提示词: 特写镜头，白天，明亮的光线。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，展现迷人锁骨和身形，她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，和她的外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，带有红色镶边，紧紧挨着，都对着镜头露出灿烂、释怀而幸福的笑容。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976136,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761329999544d93cbdf8c60805d428c33f372582fad94b.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153536Z&X-Tos-Expires=86400&X-Tos-Signature=f2963d2e7f29c4fd5d8f28c0b28b42b6350d49a63d7de90551224583126635f5&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3507ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/0217539761329999544d93cbdf8c60805d428c33f372582fad94b.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153536Z&X-Tos-Expires=86400&X-Tos-Signature=f2963d2e7f29c4fd5d8f28c0b28b42b6350d49a63d7de90551224583126635f5&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 602cd32b-889e-4ff3-8c86-a577456057b4
图片已保存到本地: /images/shot-602cd32b-889e-4ff3-8c86-a577456057b4-1753976136688.jpg
 POST /api/store-image 200 in 531ms
 ✓ Compiled in 263ms (780 modules)
 GET /storyboards 200 in 21ms
ARK_API_KEY: 存在
豆包生成图片提示词: 中景镜头，白天，明亮的光线，新家宽敞明亮的客厅里。年轻的玛雅，约22岁的印度年轻女性，身材火辣性感，长发飘逸，穿着一件时尚的丝质露肩上衣和修身长裤，正将自己获得的舞蹈奖杯和大学毕业证书并排放在一个柜子上，她的母亲普里亚，约47岁的印度女人，风韵犹存，穿着一件优雅的祖母绿现代连衣裙，和外祖母卡马拉，约82岁的印度老妇人，满头白发，穿着干净整洁的米色棉纱丽，在一旁欣慰地看着，眼中满是骄傲。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976143,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976140671499895d87efaad9c7b3a5055ad65863cf90952.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153543Z&X-Tos-Expires=86400&X-Tos-Signature=4d287bab3456e70448438020d3b2975cefe814c3ac7b4e2c4b859c8dcc17c7df&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3605ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/021753976140671499895d87efaad9c7b3a5055ad65863cf90952.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153543Z&X-Tos-Expires=86400&X-Tos-Signature=4d287bab3456e70448438020d3b2975cefe814c3ac7b4e2c4b859c8dcc17c7df&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: d05b80a1-c961-4481-b942-5bd4c93b58be
图片已保存到本地: /images/shot-d05b80a1-c961-4481-b942-5bd4c93b58be-1753976144469.jpg
 POST /api/store-image 200 in 551ms
 ✓ Compiled in 540ms (780 modules)
 GET /storyboards 200 in 34ms
ARK_API_KEY: 存在
豆包生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，面容更加苍老，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在垃圾堆里吃力地捡拾着废品，汗水浸湿了她的额头。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976196,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 02175397619681987536ccb70d6c1a12319f6b583ee3463e90298"}}
豆包 API error: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976196,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 02175397619681987536ccb70d6c1a12319f6b583ee3463e90298"}}
 POST /api/doubao 500 in 507ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/Documents/GitHub/ScriptVivid-AI/.next/cache/webpack/client-development/0.pack.gz'
ARK_API_KEY: 存在
豆包生成图片提示词: 广角镜头，白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在吃力地捡拾着废品。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976244,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 021753976244322d293cb65206e2c080ed584821a0c52876706be"}}
豆包 API error: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976244,"error":{"code":"InputTextSensitiveContentDetected","message":"The request failed because the input text may contain sensitive information. Request id: 021753976244322d293cb65206e2c080ed584821a0c52876706be"}}
 POST /api/doubao 500 in 544ms
ARK_API_KEY: 存在
豆包生成图片提示词: 白天，阳光明媚，尘土飞扬的印度街头，背景是杂乱的建筑。卡马拉，约65岁的印度老妇人，身穿更破旧的土褐色纱丽，背着装满塑料瓶的巨大麻袋，正在吃力地捡拾着废品。超高清，细节丰富，电影质感，照片级真实感，电影级光效，4K画质。
图片尺寸: 9:16
使用豆包API，尺寸: 768x1360
豆包 API 响应: {"model":"doubao-seedream-3-0-t2i-250415","created":1753976262,"data":[{"url":"https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397625890844834ed000534ac504efc334c321a785cd4b0d.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153742Z&X-Tos-Expires=86400&X-Tos-Signature=a736a9fcf36c73917e56ada5122a5b34f59107bd364fa303b2662e451ef2b426&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"}],"usage":{"generated_images":1,"output_tokens":4080,"total_tokens":4080}}

 POST /api/doubao 200 in 3644ms
存储图片: https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175397625890844834ed000534ac504efc334c321a785cd4b0d.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250731%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250731T153742Z&X-Tos-Expires=86400&X-Tos-Signature=a736a9fcf36c73917e56ada5122a5b34f59107bd364fa303b2662e451ef2b426&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D 分镜ID: 242a4635-fc30-4a7e-ba87-45ddc4567a84
图片已保存到本地: /images/shot-242a4635-fc30-4a7e-ba87-45ddc4567a84-1753976262765.jpg
 POST /api/store-image 200 in 580ms
 ✓ Compiled in 758ms (780 modules)
 GET /storyboards 200 in 62ms
