-- Create shots table for storyboard data
CREATE TABLE IF NOT EXISTS shots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  shot_number INTEGER NOT NULL,
  title VARCHAR NOT NULL,
  content TEXT,
  image_prompt TEXT,
  english_prompt TEXT,
  image_url TEXT,
  video_url TEXT,
  duration INTEGER, -- in seconds
  shot_type VARCHAR,
  notes TEXT,
  image_size VARCHAR DEFAULT 'portrait-2-3',
  generation_model VARCHAR DEFAULT 'sdxl',
  image_style VARCHAR DEFAULT 'realistic-photography',
  generation_mode VARCHAR DEFAULT 'smart',
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('draft', 'generating', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shots_project_id ON shots(project_id);
CREATE INDEX IF NOT EXISTS idx_shots_shot_number ON shots(project_id, shot_number);
CREATE INDEX IF NOT EXISTS idx_shots_status ON shots(status);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for shots table
CREATE TRIGGER update_shots_updated_at BEFORE UPDATE ON shots
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
