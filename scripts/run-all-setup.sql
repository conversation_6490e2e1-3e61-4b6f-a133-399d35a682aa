-- Step 1: Create the shots table
CREATE TABLE IF NOT EXISTS shots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  shot_number INTEGER NOT NULL,
  title VARCHAR NOT NULL,
  content TEXT,
  image_prompt TEXT,
  english_prompt TEXT,
  image_url TEXT,
  video_url TEXT,
  duration INTEGER, -- in seconds
  shot_type VARCHAR,
  notes TEXT,
  image_size VARCHAR DEFAULT 'portrait-2-3',
  generation_model VARCHAR DEFAULT 'sdxl',
  image_style VARCHAR DEFAULT 'realistic-photography',
  generation_mode VARCHAR DEFAULT 'smart',
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('draft', 'generating', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_shots_project_id ON shots(project_id);
CREATE INDEX IF NOT EXISTS idx_shots_shot_number ON shots(project_id, shot_number);
CREATE INDEX IF NOT EXISTS idx_shots_status ON shots(status);

-- Step 3: Create update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 4: Create trigger
CREATE TRIGGER update_shots_updated_at BEFORE UPDATE ON shots
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 5: Create RPC functions
CREATE OR REPLACE FUNCTION update_shot_numbers(
  project_id UUID,
  shot_updates JSONB
)
RETURNS void AS $$
DECLARE
  shot_update RECORD;
BEGIN
  FOR shot_update IN SELECT * FROM jsonb_to_recordset(shot_updates) AS x(id UUID, shot_number INTEGER)
  LOOP
    UPDATE shots 
    SET shot_number = shot_update.shot_number, updated_at = NOW()
    WHERE id = shot_update.id AND shots.project_id = update_shot_numbers.project_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_next_shot_number(project_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(
    (SELECT MAX(shot_number) + 1 FROM shots WHERE shots.project_id = get_next_shot_number.project_id),
    1
  );
END;
$$ LANGUAGE plpgsql;

-- Step 6: Insert sample project
INSERT INTO projects (id, user_id, title, description, status)
VALUES 
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Luxury Cruise Adventure', 'A romantic story on a luxury cruise ship', 'in_progress')
ON CONFLICT (id) DO NOTHING;

-- Step 7: Insert sample shots
INSERT INTO shots (
  project_id, 
  shot_number, 
  title, 
  content, 
  image_prompt, 
  english_prompt,
  duration,
  shot_type,
  notes,
  image_size,
  generation_model,
  image_style,
  status
) VALUES 
(
  '550e8400-e29b-41d4-a716-************',
  1,
  'Opening Scene - Cruise Deck',
  'Anna lies on the luxurious cruise ship deck, enjoying the golden hour sun with Marquez taking photos of her.',
  '在豪华邮轮甲板上的黄金时段，面对红色夕阳和大海。**安娜**（丰满的印度女士，穿着优雅的白色连衣裙，金色头发，黑发扎起，温柔快乐的表情）**躺在甲板上，**马奎兹**（穿着休闲的印度服装，金色丝绸衬衫，黑头发，温柔的表情，拿着彩色手机）**正在给她拍照。',
  'On the luxurious cruise ship deck at golden hour, facing the red sunset and sea. **Anna** (chubby Indian lady, wearing elegant white dress, golden hair, black hair tied up, gentle and happy expression) **lies on the deck, **Marquez** (wearing casual Indian attire, golden silk shirt, black hair, gentle expression, holding a colored phone) **is taking photos of her.',
  15,
  'Wide Shot',
  'Use natural golden hour lighting for romantic atmosphere',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  2,
  'Character Close-up - Anna',
  'Close-up of Anna''s peaceful face bathed in golden sunlight, showing her serene expression and gentle smile.',
  '安娜面部的特写镜头，在金色阳光下，显示她宁静的表情，温柔的微笑，眼睛微闭，温暖的阳光在她的脸部特征上创造柔和的光辉。',
  'Close-up shot of Anna''s face in golden hour lighting, peaceful expression, gentle smile, eyes gently closed, warm sunlight creating a soft glow on her facial features.',
  8,
  'Close-up',
  'Focus on facial expressions and lighting',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  3,
  'Marquez Photography Moment',
  'Medium shot of Marquez holding his phone, capturing the perfect moment with concentration and artistic vision.',
  '马奎兹的中景镜头，他专注地举着手机拍照，展现他的艺术眼光和专注表情。',
  'Medium shot of Marquez holding his phone with concentration, showing his artistic vision and focused expression.',
  10,
  'Medium Shot',
  'Capture the photographer''s perspective and concentration',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  4,
  'Romantic Interaction',
  'Two-shot showing both Anna and Marquez sharing a tender moment as the sun sets behind them, creating silhouettes.',
  '双人镜头显示安娜和马奎兹分享温柔时刻，夕阳在他们身后落下，创造出美丽的剪影效果。',
  'Two-shot showing Anna and Marquez sharing a tender moment, sunset falling behind them, creating beautiful silhouette effects.',
  12,
  'Two Shot',
  'Create dramatic silhouette effect with sunset backlighting',
  'landscape-16-9',
  'sdxl',
  'realistic-photography',
  'draft'
),
(
  '550e8400-e29b-41d4-a716-************',
  5,
  'Cruise Ship Establishing Shot',
  'Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky.',
  '整艘豪华邮轮的大远景镜头，在美丽的夕阳天空下航行穿过平静的海水。',
  'Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky.',
  18,
  'Extreme Wide Shot',
  'Establish the grand scale and romantic setting',
  'landscape-16-9',
  'sdxl',
  'realistic-photography',
  'draft'
);
