-- 添加runway_task_id列到shots表
-- 用于存储RUNWAY API的任务ID

-- 检查列是否已存在，如果不存在则添加
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'shots' 
        AND column_name = 'runway_task_id'
    ) THEN
        ALTER TABLE shots ADD COLUMN runway_task_id TEXT;
        RAISE NOTICE 'Added runway_task_id column to shots table';
    ELSE
        RAISE NOTICE 'runway_task_id column already exists in shots table';
    END IF;
END $$;

-- 验证列已添加
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'shots' 
AND column_name = 'runway_task_id'; 