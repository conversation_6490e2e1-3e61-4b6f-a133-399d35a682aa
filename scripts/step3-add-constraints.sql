-- 第三步：添加外键约束（如果需要的话）
-- 注意：这里我们跳过外键约束，因为 auth.users 可能不存在
-- 如果你的 Supabase 项目启用了认证，可以取消注释下面的行

-- ALTER TABLE public.profiles 
--   ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- ALTER TABLE public.projects 
--   ADD CONSTRAINT projects_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- ALTER TABLE public.storyboards 
--   ADD CONSTRAINT storyboards_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

-- ALTER TABLE public.storyboards 
--   ADD CONSTRAINT storyboards_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- ALTER TABLE public.user_settings 
--   ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- ALTER TABLE public.youtube_contents 
--   ADD CONSTRAINT youtube_contents_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_storyboards_project_id ON public.storyboards(project_id);
CREATE INDEX IF NOT EXISTS idx_storyboards_user_id ON public.storyboards(user_id);
CREATE INDEX IF NOT EXISTS idx_storyboards_order ON public.storyboards(project_id, "order");
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON public.user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_youtube_contents_user_id ON public.youtube_contents(user_id);
