-- 第二步：创建 storyboards 表
CREATE TABLE IF NOT EXISTS public.storyboards (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid NOT NULL,
  project_name text NOT NULL,
  "order" integer NOT NULL,
  content text NOT NULL,
  image_prompt text NOT NULL DEFAULT ''::text,
  image_prompt_en text NOT NULL DEFAULT ''::text,
  video_prompt text NOT NULL DEFAULT ''::text,
  video_prompt_en text NOT NULL DEFAULT ''::text,
  subtitle text NOT NULL DEFAULT ''::text,
  subtitle_en text NOT NULL DEFAULT ''::text,
  images jsonb DEFAULT '[]'::jsonb,
  videos jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id uuid,
  CONSTRAINT storyboards_pkey PRIMARY KEY (id)
);
