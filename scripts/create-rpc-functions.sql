-- Create RPC function for batch updating shot numbers
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION update_shot_numbers(
  project_id UUID,
  shot_updates JSONB
)
RETURNS void AS $$
DECLARE
  shot_update RECORD;
BEGIN
  FOR shot_update IN SELECT * FROM jsonb_to_recordset(shot_updates) AS x(id UUID, shot_number INTEGER)
  LOOP
    UPDATE shots 
    SET shot_number = shot_update.shot_number, updated_at = NOW()
    WHERE id = shot_update.id AND shots.project_id = update_shot_numbers.project_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to get next shot number for a project
CREATE OR REPLACE FUNCTION get_next_shot_number(project_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(
    (SELECT MAX(shot_number) + 1 FROM shots WHERE shots.project_id = get_next_shot_number.project_id),
    1
  );
END;
$$ LANGUAGE plpgsql;
