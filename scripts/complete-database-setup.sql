-- Step 1: Create users table (if not exists)
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  name VA<PERSON>HA<PERSON>,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Create projects table (if not exists)
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed')),
  data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create videos table (if not exists)
CREATE TABLE IF NOT EXISTS videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  url TEXT,
  duration INTEGER,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Create storyboards table (if not exists)
CREATE TABLE IF NOT EXISTS storyboards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  scenes JSONB DEFAULT '[]',
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Create shots table
CREATE TABLE IF NOT EXISTS shots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  shot_number INTEGER NOT NULL,
  title VARCHAR NOT NULL,
  content TEXT,
  image_prompt TEXT,
  english_prompt TEXT,
  image_url TEXT,
  video_url TEXT,
  duration INTEGER, -- in seconds
  shot_type VARCHAR,
  notes TEXT,
  image_size VARCHAR DEFAULT 'portrait-2-3',
  generation_model VARCHAR DEFAULT 'sdxl',
  image_style VARCHAR DEFAULT 'realistic-photography',
  generation_mode VARCHAR DEFAULT 'smart',
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('draft', 'generating', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_project_id ON videos(project_id);
CREATE INDEX IF NOT EXISTS idx_storyboards_user_id ON storyboards(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_storyboards_status ON storyboards(status);
CREATE INDEX IF NOT EXISTS idx_shots_project_id ON shots(project_id);
CREATE INDEX IF NOT EXISTS idx_shots_shot_number ON shots(project_id, shot_number);
CREATE INDEX IF NOT EXISTS idx_shots_status ON shots(status);

-- Step 7: Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 8: Create triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_storyboards_updated_at BEFORE UPDATE ON storyboards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shots_updated_at BEFORE UPDATE ON shots
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 9: Create RPC functions
CREATE OR REPLACE FUNCTION update_shot_numbers(
  project_id UUID,
  shot_updates JSONB
)
RETURNS void AS $$
DECLARE
  shot_update RECORD;
BEGIN
  FOR shot_update IN SELECT * FROM jsonb_to_recordset(shot_updates) AS x(id UUID, shot_number INTEGER)
  LOOP
    UPDATE shots 
    SET shot_number = shot_update.shot_number, updated_at = NOW()
    WHERE id = shot_update.id AND shots.project_id = update_shot_numbers.project_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_next_shot_number(project_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(
    (SELECT MAX(shot_number) + 1 FROM shots WHERE shots.project_id = get_next_shot_number.project_id),
    1
  );
END;
$$ LANGUAGE plpgsql;

-- Step 10: Insert sample users (only if they don't exist)
INSERT INTO users (id, email, name, avatar_url) 
VALUES 
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Demo User', NULL)
ON CONFLICT (email) DO NOTHING;

-- Step 11: Insert sample projects
INSERT INTO projects (id, user_id, title, description, status, data)
VALUES 
  (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'Luxury Cruise Adventure',
    'A romantic story on a luxury cruise ship',
    'in_progress',
    '{"budget": 10000, "deadline": "2024-03-01", "target_audience": "romance enthusiasts"}'
  ),
  (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'Brand Story Video',
    'Telling our company story through video',
    'draft',
    '{"style": "documentary", "duration": "3-5 minutes"}'
  )
ON CONFLICT (id) DO NOTHING;

-- Step 12: Insert sample shots
INSERT INTO shots (
  project_id, 
  shot_number, 
  title, 
  content, 
  image_prompt, 
  english_prompt,
  duration,
  shot_type,
  notes,
  image_size,
  generation_model,
  image_style,
  status
) VALUES 
(
  '550e8400-e29b-41d4-a716-************',
  1,
  'Opening Scene - Cruise Deck',
  'Anna lies on the luxurious cruise ship deck, enjoying the golden hour sun with Marquez taking photos of her.',
  '在豪华邮轮甲板上的黄金时段，面对红色夕阳和大海。**安娜**（丰满的印度女士，穿着优雅的白色连衣裙，金色头发，黑发扎起，温柔快乐的表情）**躺在甲板上，**马奎兹**（穿着休闲的印度服装，金色丝绸衬衫，黑头发，温柔的表情，拿着彩色手机）**正在给她拍照。场景：豪华邮轮甲板，木地板，金色栏杆，远处的大海。构图：中景，安娜躺在画面右侧，马奎兹在左侧拍照。光线：夕阳的多重金色反射在人们身上，形成温暖的光线，氛围：浪漫，温暖。',
  'On the luxurious cruise ship deck at golden hour, facing the red sunset and sea. **Anna** (chubby Indian lady, wearing elegant white dress, golden hair, black hair tied up, gentle and happy expression) **lies on the deck, **Marquez** (wearing casual Indian attire, golden silk shirt, black hair, gentle expression, holding a colored phone) **is taking photos of her. Scene: luxurious cruise deck, wooden floor, golden railings, distant sea. Composition: medium shot, Anna lying on the right side of the frame, Marquez on the left taking photos. Lighting: multiple golden sunset reflections on people, forming warm light, atmosphere: romantic, warm.',
  15,
  'Wide Shot',
  'Use natural golden hour lighting for romantic atmosphere',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  2,
  'Character Close-up - Anna',
  'Close-up of Anna''s peaceful face bathed in golden sunlight, showing her serene expression and gentle smile.',
  '安娜面部的特写镜头，在金色阳光下，显示她宁静的表情，温柔的微笑，眼睛微闭，温暖的阳光在她的脸部特征上创造柔和的光辉。背景虚化的邮轮甲板，重点突出她的面部表情。',
  'Close-up shot of Anna''s face in golden hour lighting, peaceful expression, gentle smile, eyes gently closed, warm sunlight creating a soft glow on her facial features. Blurred cruise deck background, focus on her facial expression.',
  8,
  'Close-up',
  'Focus on facial expressions and lighting',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  3,
  'Marquez Photography Moment',
  'Medium shot of Marquez holding his phone, capturing the perfect moment with concentration and artistic vision.',
  '马奎兹的中景镜头，他专注地举着手机拍照，展现他的艺术眼光和专注表情。穿着金色丝绸衬衫，黑发在微风中轻拂，邮轮的豪华环境作为背景，展现他作为摄影师的专业态度。',
  'Medium shot of Marquez holding his phone with concentration, showing his artistic vision and focused expression. Wearing golden silk shirt, black hair gently flowing in the breeze, luxury cruise environment as background, displaying his professional attitude as a photographer.',
  10,
  'Medium Shot',
  'Capture the photographer''s perspective and concentration',
  'portrait-2-3',
  'sdxl',
  'realistic-photography',
  'completed'
),
(
  '550e8400-e29b-41d4-a716-************',
  4,
  'Romantic Interaction',
  'Two-shot showing both Anna and Marquez sharing a tender moment as the sun sets behind them, creating silhouettes.',
  '双人镜头显示安娜和马奎兹分享温柔时刻，夕阳在他们身后落下，创造出美丽的剪影效果。他们面对面站着，手轻触，背景是壮观的海洋日落，整个画面充满浪漫氛围。',
  'Two-shot showing Anna and Marquez sharing a tender moment, sunset falling behind them, creating beautiful silhouette effects. They stand face to face, hands gently touching, with spectacular ocean sunset as background, the entire frame filled with romantic atmosphere.',
  12,
  'Two Shot',
  'Create dramatic silhouette effect with sunset backlighting',
  'landscape-16-9',
  'sdxl',
  'realistic-photography',
  'draft'
),
(
  '550e8400-e29b-41d4-a716-************',
  5,
  'Cruise Ship Establishing Shot',
  'Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky.',
  '整艘豪华邮轮的大远景镜头，在美丽的夕阳天空下航行穿过平静的海水。展现邮轮的宏伟规模和海洋的广阔，天空中有柔和的云彩，海面反射着夕阳的光芒，营造出宏大而宁静的氛围。',
  'Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky. Showing the magnificent scale of the cruise ship and the vastness of the ocean, with soft clouds in the sky, sea surface reflecting sunset light, creating a grand and peaceful atmosphere.',
  18,
  'Extreme Wide Shot',
  'Establish the grand scale and romantic setting',
  'landscape-16-9',
  'sdxl',
  'realistic-photography',
  'draft'
);

-- Step 13: Insert sample storyboards
INSERT INTO storyboards (user_id, title, description, scenes, status)
VALUES 
  (
    '550e8400-e29b-41d4-a716-************',
    'Product Demo Storyboard',
    'Visual plan for product demonstration video',
    '[
      {"id": 1, "title": "Opening Scene", "description": "Logo animation and intro", "duration": 5},
      {"id": 2, "title": "Problem Statement", "description": "Show customer pain points", "duration": 15},
      {"id": 3, "title": "Solution Reveal", "description": "Introduce our product", "duration": 20},
      {"id": 4, "title": "Feature Demo", "description": "Show key features in action", "duration": 30},
      {"id": 5, "title": "Call to Action", "description": "Encourage viewers to try product", "duration": 10}
    ]',
    'in_progress'
  ),
  (
    '550e8400-e29b-41d4-a716-************',
    'Tutorial Series Plan',
    'Educational content storyboard for tutorial videos',
    '[
      {"id": 1, "title": "Introduction", "description": "Welcome and overview", "duration": 10},
      {"id": 2, "title": "Setup", "description": "Getting started guide", "duration": 20},
      {"id": 3, "title": "Basic Usage", "description": "Core functionality demo", "duration": 25},
      {"id": 4, "title": "Advanced Tips", "description": "Pro tips and tricks", "duration": 15},
      {"id": 5, "title": "Conclusion", "description": "Summary and next steps", "duration": 5}
    ]',
    'draft'
  )
ON CONFLICT DO NOTHING;
