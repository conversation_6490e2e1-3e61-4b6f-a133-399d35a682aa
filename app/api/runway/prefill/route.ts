import { NextRequest, NextResponse } from 'next/server'

// 简易内存存储（开发环境足够使用；生产请改为数据库/缓存）
const prefillStore: Map<string, any> = new Map()

export async function POST(req: NextRequest) {
  try {
    const payload = await req.json()
    const id = crypto.randomUUID()
    prefillStore.set(id, payload)

    // 生成可被 Tampermonkey 拉取的完整地址
    const origin = req.headers.get('x-forwarded-host')
      ? `${req.headers.get('x-forwarded-proto') || 'https'}://${req.headers.get('x-forwarded-host')}`
      : req.nextUrl.origin
    const fetchUrl = `${origin}/api/runway/prefill?id=${id}`

    return NextResponse.json({ id, fetchUrl })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'invalid payload' }, { status: 400 })
  }
}

export async function GET(req: NextRequest) {
  const id = req.nextUrl.searchParams.get('id')
  if (!id) return NextResponse.json({ error: 'missing id' }, { status: 400 })
  const data = prefillStore.get(id)
  if (!data) return NextResponse.json({ error: 'not found' }, { status: 404 })
  return NextResponse.json(data)
}

