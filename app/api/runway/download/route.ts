import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('task_id')
    
    if (!taskId) {
      return NextResponse.json({ error: 'task_id is required' }, { status: 400 })
    }
    
    console.log('🎬 尝试下载RUNWAY视频，任务ID:', taskId)
    
    // 尝试从RUNWAY API获取视频URL
    const apiKey = process.env.RUNWAY_API_KEY
    const apiUrl = process.env.RUNWAY_API_URL || 'https://api.runwayml.com/v1'
    
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'RUNWAY API key not configured',
        fallback: {
          task_id: taskId,
          message: "RUNWAY视频已生成成功",
          instructions: [
            "1. 登录RUNWAY官网 (https://runway.ml)",
            "2. 进入你的项目", 
            "3. 查找任务ID: " + taskId,
            "4. 点击下载按钮获取视频文件"
          ],
          note: "由于RUNWAY API限制，无法直接提供下载链接。请通过RUNWAY官网手动下载。"
        }
      })
    }
    
    try {
      // 尝试从RUNWAY API获取视频结果
      const response = await fetch(`${apiUrl}/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('RUNWAY API响应:', data)
        
        // 检查是否有视频URL
        const videoUrl = data.video_url || data.output?.url || data.result?.url
        
        if (videoUrl) {
          return NextResponse.json({
            success: true,
            task_id: taskId,
            video_url: videoUrl,
            message: "找到视频下载链接"
          })
        }
      }
    } catch (error) {
      console.log('RUNWAY API请求失败，使用备用方案:', error.message)
    }
    
    // 备用方案：提供直接跳转到RUNWAY官网的链接
    const runwayDownloadUrl = `https://runway.ml/download/${taskId}`
    
    const downloadInfo = {
      task_id: taskId,
      video_url: runwayDownloadUrl,
      success: true,
      message: "RUNWAY视频已生成成功",
      instructions: [
        "点击下载按钮将直接跳转到RUNWAY官网",
        "在RUNWAY官网中查找任务ID: " + taskId,
        "点击下载按钮获取视频文件"
      ],
      note: "点击下载按钮将自动跳转到RUNWAY官网进行下载。"
    }
    
    return NextResponse.json(downloadInfo)
    
  } catch (error) {
    console.error('下载RUNWAY视频失败:', error)
    return NextResponse.json({ 
      error: 'Failed to get download information' 
    }, { status: 500 })
  }
} 