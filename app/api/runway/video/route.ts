import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { promptImage, promptText, seed, model, watermark, duration, ratio, style, motion_vector } = body

    // 验证必需的字段
    if (!promptText) {
      return NextResponse.json(
        { error: 'promptText is required' },
        { status: 400 }
      )
    }

    // 获取环境变量中的API密钥和URL
    const runwayApiKey = process.env.RUNWAY_API_KEY
    const runwayApiUrl = process.env.RUNWAY_API_URL || 'https://api.apicore.ai/runway/pro/generate'
    
    if (!runwayApiKey) {
      console.error('RUNWAY_API_KEY not found in environment variables')
      return NextResponse.json(
        { error: 'RUNWAY API key not configured' },
        { status: 500 }
      )
    }

    console.log('RUNWAY API Key exists:', !!runwayApiKey)
    console.log('RUNWAY API URL:', runwayApiUrl)
    console.log('Requesting video generation with:', { promptImage, promptText, model, duration, ratio, style })

    // 处理图片URL
    let imageUrl = promptImage
    if (imageUrl && imageUrl.startsWith('/')) {
      const host = request.headers.get('host')
      const protocol = request.headers.get('x-forwarded-proto') || 'http'
      imageUrl = `${protocol}://${host}${imageUrl}`
      console.log('转换相对路径为完整URL:', promptImage, '->', imageUrl)
    }

    // 只使用RUNWAY API
    try {
      console.log('Using RUNWAY API for video generation...')
      
      // 构建Runway API请求体 - 支持图片输入
      const runwayRequestBody = {
        callback_url: "", // 可选，如果需要回调可以设置
        ratio: ratio || "16:9",
        prompt: promptText,
        style: style || "cinematic",
        model: model || "gen3",
        // 添加图片输入支持
        ...(imageUrl && { image_url: imageUrl }),
        options: {
          seconds: duration || 10,
          motion_vector: motion_vector || {
            x: 0,
            y: 0.4,
            z: 0,
            r: -6,
            bg_x_pan: 0,
            bg_y_pan: 0
          }
        }
      }

      console.log('RUNWAY API Request Body:', runwayRequestBody)

      const runwayResponse = await fetch(runwayApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${runwayApiKey}`,
        },
        body: JSON.stringify(runwayRequestBody),
      })

      console.log('RUNWAY API Response Status:', runwayResponse.status)

      if (runwayResponse.ok) {
        const data = await runwayResponse.json()
        console.log('RUNWAY API Success:', data)
        
        // 返回标准化的响应格式
        return NextResponse.json({
          success: true,
          task_id: data.data?.task_id,
          status: data.data?.status,
          message: data.msg,
          api: 'runway'
        })
      } else {
        const errorText = await runwayResponse.text()
        console.error('RUNWAY API Error:', errorText)
        return NextResponse.json(
          { error: `RUNWAY API error: ${runwayResponse.status} ${errorText}` },
          { status: runwayResponse.status }
        )
      }
    } catch (error) {
      console.error('Runway API failed:', error)
      return NextResponse.json(
        { error: 'RUNWAY API request failed' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in video generation API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}