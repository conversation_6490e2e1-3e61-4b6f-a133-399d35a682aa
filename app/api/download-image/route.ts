import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json()
    
    if (!imageUrl) {
      return NextResponse.json({ error: "Image URL is required" }, { status: 400 })
    }

    console.log("代理下载图片:", imageUrl)

    // 检查是否是本地路径
    if (imageUrl.startsWith('/images/')) {
      console.log("检测到本地图片路径，直接返回错误让前端使用其他方法")
      return NextResponse.json({ error: "Local image path detected" }, { status: 400 })
    }

    // 代理获取远程图片
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    if (!response.ok) {
      console.error("获取图片失败:", response.status, response.statusText)
      return NextResponse.json({ error: "Failed to fetch image" }, { status: response.status })
    }

    const contentType = response.headers.get('content-type') || 'image/png'
    const buffer = await response.arrayBuffer()

    console.log("图片获取成功，大小:", buffer.byteLength, "类型:", contentType)

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': buffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=31536000',
      },
    })

  } catch (error) {
    console.error("代理下载图片错误:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 