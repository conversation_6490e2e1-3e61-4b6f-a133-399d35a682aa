import { NextRequest, NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import { join } from 'path'

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, shotId } = await request.json()
    
    if (!imageUrl || !shotId) {
      return NextResponse.json({ error: "Image URL and shot ID are required" }, { status: 400 })
    }

    console.log("存储图片:", imageUrl, "分镜ID:", shotId)

    // 下载图片
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    if (!response.ok) {
      console.error("下载图片失败:", response.status, response.statusText)
      return NextResponse.json({ error: "Failed to download image" }, { status: response.status })
    }

    const buffer = await response.arrayBuffer()
    const uint8Array = new Uint8Array(buffer)
    
    // 生成文件名
    const timestamp = Date.now()
    const extension = imageUrl.includes('.png') ? 'png' : 'jpg'
    const filename = `shot-${shotId}-${timestamp}.${extension}`
    
    // 保存到 public/images 目录
    const filepath = join(process.cwd(), 'public', 'images', filename)
    
    await writeFile(filepath, uint8Array)
    
    // 返回本地URL
    const localUrl = `/images/${filename}`
    
    console.log("图片已保存到本地:", localUrl)
    
    return NextResponse.json({
      success: true,
      localUrl: localUrl,
      originalUrl: imageUrl
    })

  } catch (error) {
    console.error("存储图片失败:", error)
    return NextResponse.json(
      { error: "Failed to store image" },
      { status: 500 }
    )
  }
} 