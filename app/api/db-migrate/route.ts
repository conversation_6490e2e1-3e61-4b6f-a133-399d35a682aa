import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // 检查列是否已存在
    const { data: existingColumns, error: checkError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'shots')
      .eq('column_name', 'runway_task_id')
    
    if (checkError) {
      console.error('检查列存在性失败:', checkError)
      return NextResponse.json({ error: 'Failed to check column existence' }, { status: 500 })
    }
    
    if (existingColumns && existingColumns.length > 0) {
      console.log('runway_task_id列已存在')
      return NextResponse.json({ 
        success: true, 
        message: 'runway_task_id column already exists' 
      })
    }
    
    // 添加新列
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE shots ADD COLUMN runway_task_id TEXT;'
    })
    
    if (alterError) {
      console.error('添加列失败:', alterError)
      return NextResponse.json({ error: 'Failed to add column' }, { status: 500 })
    }
    
    console.log('成功添加runway_task_id列')
    return NextResponse.json({ 
      success: true, 
      message: 'Successfully added runway_task_id column' 
    })
    
  } catch (error) {
    console.error('数据库迁移失败:', error)
    return NextResponse.json({ error: 'Database migration failed' }, { status: 500 })
  }
} 