import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { image_url, prompt } = body

    // 验证必需的字段
    if (!image_url || !prompt) {
      return NextResponse.json(
        { error: 'image_url and prompt are required' },
        { status: 400 }
      )
    }

    // 获取环境变量中的API密钥
    const apiKey = process.env.wan_api_key || process.env.WAN_API_KEY
    if (!apiKey) {
      console.error('wan_api_key or WAN_API_KEY not found in environment variables')
      return NextResponse.json(
        { error: 'WAN API key not configured' },
        { status: 500 }
      )
    }

    console.log('WAN API Key exists:', !!apiKey)
    
    // 转换相对路径为完整URL
    let fullImageUrl = image_url
    if (image_url.startsWith('/')) {
      const host = request.headers.get('host')
      const protocol = request.headers.get('x-forwarded-proto') || 'http'
      fullImageUrl = `${protocol}://${host}${image_url}`
      console.log('转换相对路径为完整URL:', image_url, '->', fullImageUrl)
    }
    
    console.log('Requesting video generation with:', { image_url: fullImageUrl, prompt })

    // 调用WAN API
    const response = await fetch('https://queue.fal.run/fal-ai/wan/v2.2-a14b/image-to-video/turbo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Key ${apiKey}`,
      },
      body: JSON.stringify({
        image_url: fullImageUrl,
        prompt
      }),
    })

    console.log('WAN API Response Status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('WAN API Error:', errorText)
      return NextResponse.json(
        { error: `WAN API error: ${response.status} ${errorText}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('WAN API Success:', data)

    return NextResponse.json(data)

  } catch (error) {
    console.error('Error in WAN video generation API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}