import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const requestId = searchParams.get('request_id')

    if (!requestId) {
      return NextResponse.json(
        { error: 'request_id is required' },
        { status: 400 }
      )
    }

    // 获取环境变量中的API密钥
    const apiKey = process.env.wan_api_key || process.env.WAN_API_KEY
    if (!apiKey) {
      console.error('wan_api_key or WAN_API_KEY not found in environment variables')
      return NextResponse.json(
        { error: 'WAN API key not configured' },
        { status: 500 }
      )
    }

    console.log('Checking WAN video status for request:', requestId)

    // 检查视频生成状态（增加重试机制）
    const statusUrl = `https://queue.fal.run/fal-ai/wan/requests/${requestId}/status`
    
    let retryCount = 0
    const maxRetries = 2
    
    while (retryCount <= maxRetries) {
      try {
        // 添加超时控制
        const controller = new AbortController()
        const timeoutId = setTimeout(() => {
          console.log('⏰ WAN API请求超时，中止请求')
          controller.abort()
        }, 10000) // 10秒超时
        
        const response = await fetch(statusUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${apiKey}`,
          },
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        console.log('WAN Status API Response Status:', response.status)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('WAN Status API Error:', errorText)
          return NextResponse.json(
            { error: `WAN Status API error: ${response.status} ${errorText}` },
            { status: response.status }
          )
        }

        const data = await response.json()
        console.log('WAN Status API Success:', data)
        return NextResponse.json(data)
        
      } catch (error: any) {
        retryCount++
        console.error(`❌ WAN API请求失败 (重试 ${retryCount}/${maxRetries}):`, error.message)
        
        // 如果是网络错误且还有重试机会
        if ((error.name === 'AbortError' || error.message.includes('fetch failed') || error.message.includes('SocketError')) && retryCount <= maxRetries) {
          const retryDelay = 2000 * retryCount // 2秒、4秒递增
          console.log(`🔄 ${retryDelay/1000} 秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        } else {
          // 其他错误或重试次数用完
          console.error('WAN API Status最终失败:', error)
          return NextResponse.json(
            { error: `Network error: ${error.message}` },
            { status: 500 }
          )
        }
      }
    }

  } catch (error) {
    console.error('Error in WAN status check API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { request_id } = body

    if (!request_id) {
      return NextResponse.json(
        { error: 'request_id is required' },
        { status: 400 }
      )
    }

    // 获取环境变量中的API密钥
    const apiKey = process.env.wan_api_key || process.env.WAN_API_KEY
    if (!apiKey) {
      console.error('wan_api_key or WAN_API_KEY not found in environment variables')
      return NextResponse.json(
        { error: 'WAN API key not configured' },
        { status: 500 }
      )
    }

    console.log('Getting WAN video result for request:', request_id)

    // 获取最终结果（增加重试机制）
    const resultUrl = `https://queue.fal.run/fal-ai/wan/requests/${request_id}`
    
    let retryCount = 0
    const maxRetries = 2
    
    while (retryCount <= maxRetries) {
      try {
        // 添加超时控制
        const controller = new AbortController()
        const timeoutId = setTimeout(() => {
          console.log('⏰ WAN Result API请求超时，中止请求')
          controller.abort()
        }, 15000) // 15秒超时（获取结果可能需要更长时间）
        
        const response = await fetch(resultUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${apiKey}`,
          },
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        console.log('WAN Result API Response Status:', response.status)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('WAN Result API Error:', errorText)
          return NextResponse.json(
            { error: `WAN Result API error: ${response.status} ${errorText}` },
            { status: response.status }
          )
        }

        const data = await response.json()
        console.log('WAN Result API Success:', data)
        return NextResponse.json(data)
        
      } catch (error: any) {
        retryCount++
        console.error(`❌ WAN Result API请求失败 (重试 ${retryCount}/${maxRetries}):`, error.message)
        
        // 如果是网络错误且还有重试机会
        if ((error.name === 'AbortError' || error.message.includes('fetch failed') || error.message.includes('SocketError')) && retryCount <= maxRetries) {
          const retryDelay = 3000 * retryCount // 3秒、6秒递增
          console.log(`🔄 ${retryDelay/1000} 秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        } else {
          // 其他错误或重试次数用完
          console.error('WAN Result API最终失败:', error)
          return NextResponse.json(
            { error: `Network error: ${error.message}` },
            { status: 500 }
          )
        }
      }
    }

  } catch (error) {
    console.error('Error in WAN result API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}