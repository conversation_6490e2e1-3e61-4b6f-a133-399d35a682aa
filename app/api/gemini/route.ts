import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { story, rules } = await request.json()
    
    // 使用正确的环境变量名称，匹配 .env.local 文件
    const GEMINI_API_KEY = process.env.OPENROUTER_API_KEY
    const GEMINI_API_URL = "https://openrouter.ai/api/v1/chat/completions"
    
    console.log("GEMINI_API_KEY:", GEMINI_API_KEY ? "存在" : "undefined")
    console.log("GEMINI_API_URL:", GEMINI_API_URL)
    
    if (!GEMINI_API_KEY || !GEMINI_API_URL) {
      return NextResponse.json({ error: "API KEY or URL missing" }, { status: 500 })
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 60000) // 增加到60秒超时

    try {
      const res = await fetch(GEMINI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${GEMINI_API_KEY}`,
        },
        body: JSON.stringify({
          model: "google/gemini-2.0-flash-001",
          messages: [
            {
              role: "user",
              content: `${rules}\n\n${story}`
            }
          ],
          max_tokens: 8000, // 增加token数量，允许生成更多分镜
          temperature: 0.2, // 降低温度，提高一致性，减少思考时间
          top_p: 0.9, // 添加top_p参数，优化输出质量
          stream: false // 确保不使用流式输出
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!res.ok) {
        const errText = await res.text()
        console.log("OpenRouter error:", errText)
        
        // 特殊处理402错误（付费问题）
        if (res.status === 402) {
          return NextResponse.json({ 
            error: "API账户余额不足，请检查OpenRouter账户充值状态" 
          }, { status: 402 })
        }
        
        // 特殊处理401错误（认证问题）
        if (res.status === 401) {
          return NextResponse.json({ 
            error: "API密钥无效，请检查OpenRouter API Key配置" 
          }, { status: 401 })
        }
        
        // 特殊处理429错误（限流）
        if (res.status === 429) {
          return NextResponse.json({ 
            error: "API请求频率过高，请稍后重试" 
          }, { status: 429 })
        }
        
        return NextResponse.json({ 
          error: `OpenRouter API错误 (${res.status}): ${errText}` 
        }, { status: 500 })
      }

      const data = await res.json()
      console.log("OpenRouter 响应:", JSON.stringify(data, null, 2))
      return NextResponse.json(data)
      
    } catch (fetchError: any) {
      clearTimeout(timeoutId)
      if (fetchError.name === 'AbortError') {
        console.log("请求超时")
        return NextResponse.json({ error: "请求超时，请重试" }, { status: 408 })
      }
      throw fetchError
    }
    
  } catch (error: any) {
    console.log("Gemini API 错误:", error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}