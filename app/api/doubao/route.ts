import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { prompt, size = "1024x1024" } = await request.json()
    
    const ARK_API_KEY = process.env.ARK_API_KEY
    const ARK_API_URL = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
    
    console.log("ARK_API_KEY:", ARK_API_KEY ? "存在" : "undefined")
    console.log("豆包生成图片提示词:", prompt)
    console.log("图片尺寸:", size)
    
    if (!ARK_API_KEY) {
      return NextResponse.json({ error: "ARK API KEY missing" }, { status: 500 })
    }

    if (!prompt || !prompt.trim()) {
      return NextResponse.json({ error: "图片提示词不能为空" }, { status: 400 })
    }

    // 豆包支持的尺寸选项映射 - 只保留最常用的5种
    const sizeMapping: Record<string, string> = {
      "1:1": "1024x1024",
      "2:3": "832x1248", 
      "3:2": "1248x832",
      "9:16": "768x1360",
      "16:9": "1360x768"
    }
    
    const mappedSize = sizeMapping[size] || "1024x1024"
    console.log("使用豆包API，尺寸:", mappedSize)

    const response = await fetch(ARK_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${ARK_API_KEY}`
      },
      body: JSON.stringify({
        model: "doubao-seedream-3-0-t2i-250415",
        prompt: prompt,
        response_format: "url",
        size: mappedSize,
        guidance_scale: 2.5,
        watermark: true
      })
    })

    const responseText = await response.text()
    console.log("豆包 API 响应:", responseText)

    if (!response.ok) {
      console.error("豆包 API error:", responseText)
      return NextResponse.json({ error: "图片生成失败", details: responseText }, { status: 500 })
    }

    const result = JSON.parse(responseText)
    
    // 转换为统一格式，匹配FLUX API的响应格式
    const formattedResult = {
      data: [{
        url: result.data?.[0]?.url || "",
        revised_prompt: prompt // 豆包可能没有revised_prompt，使用原prompt
      }],
      created: result.created || Date.now()
    }
    
    return NextResponse.json(formattedResult)

  } catch (error) {
    console.error("豆包 route error:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
} 