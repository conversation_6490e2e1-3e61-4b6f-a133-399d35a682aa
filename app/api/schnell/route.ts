import { NextRequest, NextResponse } from "next/server"

const SCHNELL_API_KEY = process.env.schnell_api_key

export async function POST(request: NextRequest) {
  if (!SCHNELL_API_KEY) {
    console.error("❌ Schnell API key not configured")
    return NextResponse.json(
      { error: "Schnell API key not configured" },
      { status: 500 }
    )
  }

  try {
    const { prompt, size = "landscape_4_3" } = await request.json()

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      )
    }

    console.log("🚀 Schnell API 请求开始")
    console.log("📝 提示词:", prompt)
    console.log("📐 图片尺寸:", size)

    // 映射图片尺寸到fal.ai支持的格式 - 只保留最常用的5种
    let imageSize = "landscape_4_3"
    switch (size) {
      case "1:1":
        imageSize = "square_hd"
        break
      case "2:3":
        imageSize = "portrait_4_3"
        break
      case "3:2":
        imageSize = "landscape_4_3"
        break
      case "9:16":
        imageSize = "portrait_16_9"
        break
      case "16:9":
        imageSize = "landscape_16_9"
        break
      default:
        imageSize = "landscape_4_3"
    }

    console.log("🔄 映射后的图片尺寸:", imageSize)

    // 设置超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 60000) // 增加到60秒超时

    try {
      // 步骤1: 提交请求到队列
      // 记录完整的请求信息
      const requestBody = {
        prompt: prompt,
        image_size: imageSize,
        num_inference_steps: 4,
        guidance_scale: 3.5,
        num_images: 1,
        enable_safety_checker: true,
        output_format: "jpeg"
      }
      console.log("📤 发送请求体:", JSON.stringify(requestBody, null, 2))

      const submitResponse = await fetch("https://fal.run/fal-ai/flux/schnell", {
        method: "POST",
        headers: {
          "Authorization": `Key ${SCHNELL_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: prompt,
          image_size: imageSize,
          num_inference_steps: 4,
          guidance_scale: 3.5,
          num_images: 1,
          enable_safety_checker: true,
          output_format: "jpeg"
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!submitResponse.ok) {
        const errorText = await submitResponse.text()
        console.error("❌ Schnell API 提交失败:", {
          status: submitResponse.status,
          statusText: submitResponse.statusText,
          headers: Object.fromEntries(submitResponse.headers.entries()),
          error: errorText
        })
        
        let errorMessage = "图片生成失败"
        if (submitResponse.status === 401) {
          errorMessage = "API 密钥无效，请检查配置"
        } else if (submitResponse.status === 429) {
          errorMessage = "请求过于频繁，请稍后重试"
        } else if (submitResponse.status >= 500) {
          errorMessage = "服务器错误，请稍后重试"
        }
        
        return NextResponse.json(
          { error: errorMessage, details: errorText },
          { status: submitResponse.status }
        )
      }

      let result
      try {
        result = await submitResponse.json()
        console.log("✅ Schnell API 响应成功")
        console.log("📊 完整响应:", JSON.stringify(result, null, 2))
        console.log("📍 响应头:", Object.fromEntries(submitResponse.headers.entries()))
      } catch (parseError) {
        console.error("❌ JSON解析失败:", parseError)
        console.log("📄 原始响应:", await submitResponse.text())
        throw new Error("API响应格式错误")
      }

      // 检查是否是队列响应还是直接结果
      if (result.request_id) {
        // 队列模式 - 需要轮询结果
        console.log("🔄 队列模式，request_id:", result.request_id)
        return NextResponse.json(
          { error: "队列模式暂不支持，请稍后重试" },
          { status: 500 }
        )
      }

      // 直接结果模式
      console.log("⏱️ 生成时间:", result.timings?.inference || "未知")

      // 验证响应格式
      if (!result.images || !Array.isArray(result.images) || result.images.length === 0) {
        console.error("❌ Schnell API 返回格式无效:", result)
        return NextResponse.json(
          { error: "API返回数据格式错误", details: result },
          { status: 500 }
        )
      }

      // 转换为统一的响应格式（兼容现有代码）
      const transformedResponse = {
        data: result.images.map((image: any) => ({
          url: image.url,
          width: image.width,
          height: image.height,
          content_type: image.content_type || "image/jpeg"
        })),
        timings: result.timings,
        seed: result.seed,
        has_nsfw_concepts: result.has_nsfw_concepts,
        prompt: result.prompt || prompt
      }

      console.log("🖼️ 生成的图片URL:", transformedResponse.data[0]?.url)
      console.log("🎯 Schnell API 调用完成")

      return NextResponse.json(transformedResponse)

    } catch (fetchError: any) {
      clearTimeout(timeoutId)
      if (fetchError.name === 'AbortError') {
        console.error("Schnell API 请求超时")
        return NextResponse.json(
          { error: "图片生成超时，请重试" },
          { status: 408 }
        )
      }
      console.error("❌ 网络请求失败:", {
        name: fetchError.name,
        message: fetchError.message,
        stack: fetchError.stack
      })
      throw fetchError
    }

  } catch (error: any) {
    console.error("❌ Schnell API 调用失败:", error)
    
    return NextResponse.json(
      { 
        error: error.message || "图片生成失败", 
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined 
      },
      { status: 500 }
    )
  }
}