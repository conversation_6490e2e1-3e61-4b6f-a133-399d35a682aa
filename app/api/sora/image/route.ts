import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { prompt, model = 'gpt-image-1', size = '1024x1024', n = 1 } = body

    console.log('🎨 开始SORA图片生成:', {
      prompt,
      model,
      size,
      n
    })

    // 获取SORA API配置
    const apiKey = process.env.SORA_API_KEY
    const apiUrl = process.env.SORA_API_URL || 'https://api.apicore.ai/v1/images/generations'

    if (!apiKey) {
      console.error('❌ SORA API密钥未配置')
      return NextResponse.json({
        error: 'SORA API key not configured'
      }, { status: 500 })
    }

    // 构建请求体
    const requestBody = {
      prompt: prompt,
      n: n,
      model: model,
      size: size
    }

    console.log('📤 发送SORA API请求:', {
      url: apiUrl,
      model: requestBody.model,
      size: requestBody.size
    })

    // 调用SORA API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ SORA API请求失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })

      return NextResponse.json({
        error: `SORA API request failed: ${response.status} ${response.statusText}`,
        details: errorText
      }, { status: response.status })
    }

    const data = await response.json()
    console.log('✅ SORA API响应:', data)

    // 提取图片信息
    const imageUrl = data.data?.[0]?.url || data.url

    if (!imageUrl) {
      console.error('❌ SORA API响应中缺少图片URL')
      return NextResponse.json({
        error: 'No image URL in SORA API response'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      image_url: imageUrl,
      api: 'sora',
      message: 'SORA图片生成成功'
    })

  } catch (error: any) {
    console.error('❌ SORA图片生成失败:', error)
    return NextResponse.json({
      error: 'Failed to generate SORA image',
      details: error.message
    }, { status: 500 })
  }
} 