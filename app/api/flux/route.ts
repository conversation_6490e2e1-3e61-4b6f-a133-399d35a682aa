import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { prompt, size = "9:16" } = await request.json()
    
    const FLUX_API_KEY = process.env.FLUX_API_KEY
    const FLUX_API_URL = "https://api.apicore.ai/v1/images/generations"
    
    console.log("FLUX_API_KEY:", FLUX_API_KEY ? "存在" : "undefined")
    console.log("生成图片提示词:", prompt)
    console.log("图片尺寸:", size)
    
    if (!FLUX_API_KEY) {
      return NextResponse.json({ error: "FLUX API KEY missing" }, { status: 500 })
    }

    if (!prompt || !prompt.trim()) {
      return NextResponse.json({ error: "图片提示词不能为空" }, { status: 400 })
    }

    // APICore flux-kontext-pro 支持的尺寸选项 - 只保留最常用的5种
    const validSizes = ["1:1", "2:3", "3:2", "9:16", "16:9"]
    
    if (!validSizes.includes(size)) {
      return NextResponse.json({ error: `不支持的尺寸: ${size}。支持的尺寸: ${validSizes.join(", ")}` }, { status: 400 })
    }

    console.log("使用APICore flux-kontext-pro模型，aspect_ratio:", size)
    console.log("📤 FLUX API 请求体:", JSON.stringify({
      model: "flux-kontext-pro",
      prompt: prompt,
      aspect_ratio: size
    }, null, 2))

    const response = await fetch(FLUX_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${FLUX_API_KEY}`
      },
      body: JSON.stringify({
        model: "flux-kontext-pro",
        prompt: prompt,
        aspect_ratio: size
      })
    })

    const responseText = await response.text()
    console.log("FLUX API 响应:", responseText)

    if (!response.ok) {
      console.error("FLUX API error:", responseText)
      return NextResponse.json({ error: "图片生成失败", details: responseText }, { status: 500 })
    }

    const result = JSON.parse(responseText)
    return NextResponse.json(result)

  } catch (error) {
    console.error("Flux route error:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
} 