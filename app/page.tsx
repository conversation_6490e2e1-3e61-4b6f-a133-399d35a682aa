'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Video, 
  Sparkles, 
  Download, 
  Brain, 
  Zap, 
  ArrowRight, 
  Play,
  Image as ImageIcon,
  FileText,
  Users,
  Star
} from 'lucide-react'

export default function Home() {
  const [isHovered, setIsHovered] = useState<string | null>(null)

  const features = [
    {
      id: 'ai-analysis',
      icon: Brain,
      title: 'AI Story Analysis',
      description: 'Transform your story into professional shot sequences with advanced AI technology',
      href: '/storyboards',
      color: 'from-blue-500 to-purple-600',
      badge: 'Core Feature'
    },
    {
      id: 'image-generation',
      icon: ImageIcon,
      title: 'AI Image Generation',
      description: 'Generate high-quality cinematic images based on shot content with multiple styles',
      href: '/storyboards',
      color: 'from-green-500 to-teal-600',
      badge: 'AI Powered'
    },
    {
      id: 'video-tools',
      icon: Video,
      title: 'Video Tools',
      description: 'Professional video download, analysis and processing tools with multi-format support',
      href: '/video-download',
      color: 'from-orange-500 to-red-600',
      badge: 'Professional'
    },
    {
      id: 'storyboard-management',
      icon: FileText,
      title: 'Storyboard Management',
      description: 'Complete project management and shot editing with team collaboration support',
      href: '/storyboards',
      color: 'from-purple-500 to-pink-600',
      badge: 'Management'
    }
  ]

  const stats = [
    { label: 'AI Models', value: '3+', description: 'Supported AI Models' },
    { label: 'Image Sizes', value: '8+', description: 'Supported Formats' },
    { label: 'Processing Speed', value: '<30s', description: 'Average Generation Time' },
    { label: 'User Satisfaction', value: '98%', description: 'User Rating' }
  ]

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 hero-gradient" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center">
                <Video className="w-8 h-8 text-gray-900" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              ScriptVivid AI
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Professional AI-powered video script analysis and storyboard generation platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/storyboards">
                <Button size="lg" className="bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-500 hover:to-orange-600 font-semibold">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Start AI Analysis
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/video-download">
                <Button size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white">
                  <Download className="w-5 h-5 mr-2" />
                  Video Tools
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Powerful Features, Professional Experience
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            From script analysis to storyboard generation, from image creation to project management, 
            ScriptVivid AI provides a complete video production solution
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature) => (
            <Link key={feature.id} href={feature.href}>
              <Card 
                className={`glass-card h-full cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                  isHovered === feature.id ? 'ring-2 ring-yellow-400' : ''
                }`}
                onMouseEnter={() => setIsHovered(feature.id)}
                onMouseLeave={() => setIsHovered(null)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs bg-gray-800 text-gray-300">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-white text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400 text-sm leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {stat.value}
              </div>
              <div className="text-sm font-medium text-gray-300 mb-1">
                {stat.label}
              </div>
              <div className="text-xs text-gray-500">
                {stat.description}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <Card className="glass-card">
          <CardContent className="p-8 md:p-12 text-center">
            <div className="max-w-3xl mx-auto">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center">
                  <Star className="w-8 h-8 text-gray-900" />
                </div>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                Ready to Start Your AI Video Production Journey?
              </h3>
              <p className="text-lg text-gray-300 mb-6">
                Experience the power of ScriptVivid AI and transform your creative ideas into professional video content
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/storyboards">
                  <Button size="lg" className="bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-500 hover:to-orange-600 font-semibold">
                    <Play className="w-5 h-5 mr-2" />
                    Get Started Now
                  </Button>
                </Link>
                <Link href="/video-download">
                  <Button size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white">
                    <Download className="w-5 h-5 mr-2" />
                    Explore Tools
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="bg-gray-900/50 border-t border-gray-800 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3">
                <Video className="w-5 h-5 text-gray-900" />
              </div>
              <span className="text-xl font-bold text-white">ScriptVivid AI</span>
            </div>
            <p className="text-gray-400 text-sm">
              © 2024 ScriptVivid AI. Professional AI-driven video production platform
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
