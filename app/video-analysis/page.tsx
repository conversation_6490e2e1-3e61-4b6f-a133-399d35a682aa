"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Sparkles } from "lucide-react"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { db } from "@/lib/database"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// 新增分镜类型（仅用于解析）
interface GeminiShot {
  index: number;
  content: string;
  imagePrompt: string;
  videoPrompt: string;
  subtitle: string;
}

// 首帧模式规则字符串
const GEMINI_RULES_SINGLE_FRAME = `
你是一个专业的分镜师。请将以下故事内容拆解成分镜头序列。

**重要指导原则：**
- 请尽可能详细地拆解故事，每个重要场景、动作、对话都应该有独立的分镜
- 对于复杂的场景，请拆解成多个分镜（例如：人物表情变化、镜头切换、动作序列等）
- 目标是生成10-25个分镜，确保故事的完整性和流畅性
- 不要省略任何重要的故事节点

你的任务是根据我提供的**视频脚本内容**，将每一个场景（或一个场景内的多个关键瞬间）拆解成独立的图片，并为每张图片生成详细的、用于图像生成模型的提示词。

**视频提示词生成规则（重要）：**

请使用以下专业的单图片激活视频提示词模板：

**核心理念：场景激活三要素**
- **主体动态化**：让人物或主要物体动起来，注重微小而真实的动作
- **环境氛围化**：让背景活起来，增加画面真实感和沉浸感  
- **镜头电影化**：给静态画面增加导演视角，引导观众注意力

**通用结构：**
\`[核心画面描述] + [主体动态化指令] + [环境氛围化指令] + [镜头电影化指令] + [风格与细节指令]\`

**实用格式：**
\`[核心画面描述]，[主体动作/表情的细微变化]。[环境元素的动态效果]。[镜头运动方式]。[风格与画质]。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。\`

**各部分详解：**
1. **核心画面描述**：精准描述场景、人物、穿着、构图
2. **主体动态化**：微小真实的动作（如：她的眼神中流露出绝望、轻轻眨眼、嘴角微微抽动、一滴眼泪滑落、胸口轻微起伏等）
3. **环境氛围化**：背景元素动态化（如：暴雨持续落下、路灯光影轻轻晃动、阳光透过窗户、尘埃在光柱中飘动等）
4. **镜头电影化**：缓慢微妙的镜头运动（如：镜头极其缓慢地向前推进、向后拉远、从左向右平移、固定机位等）
5. **风格细节**：专业术语定义画面质感（如：电影感、8K画质、温暖色调、柔和光线、情绪化等）

**时间分段要求：**
- 可以在提示词中加入时间分段描述，例如：前3秒人物缓缓低头，后2秒镜头缓慢推进
- 时间分段要自然流畅，符合现实运动规律
- 每个时间段内的动作要连贯，避免突兀的跳跃

**视频提示词要求：**
- 【强制要求】必须使用中文，严禁任何英文单词，必须是完整的中文句子
- 简洁明了
- 必须严格按照上述单图片激活模板生成
- 包含五个核心要素：核心画面描述 + 主体动态化 + 环境氛围化 + 镜头电影化 + 风格细节
- 注重微小而真实的动作，避免大幅度位移
- 让背景环境也具有动态效果
- 使用缓慢微妙的镜头运动
- 适合AI视频生成工具使用
- 结尾必须包含：视频画面连贯，流畅，符合现实运动规则，不要出现其他角色

对于每个分镜，请按照以下格式输出：

**分镜1**
分镜内容：[用中文描述这个分镜的场景和动作]
图片提示词：[严格按照以下模板格式输出，注意：请将所有方括号内的占位符替换为具体内容，不要保留方括号]：

镜头类型，光线条件，时间，在场景描述，背景是背景细节描述。

主要角色描述1（人物特征，服装描述）动作描述，表情描述。  
主要角色描述2（人物特征，服装描述）动作描述，表情描述。  
...（如有更多主要角色，继续添加）。

风格：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
光照：自然光照，柔和且均匀，微妙且真实的光影。
色彩：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
画质：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。

**重要说明：**
- 请将上述模板中的所有 [占位符] 替换为具体内容，不要保留方括号
- 镜头类型：例如：全身镜头，中景镜头，特写镜头
- 光线条件：例如：在明亮的日光下，在柔和自然光线，天空朦胧
- 时间：例如：白天，夜晚，黄昏，清晨
- 场景描述：例如：一个繁华的印度城市街道，一间破旧的印度乡村房屋内部
- 背景细节描述：例如：背景是带有"SAVCINO"和"GRIANET"标志的奢侈品店橱窗
- 主要角色描述：包含角色名称、人物特征、服装描述、动作描述、表情描述
- 特定物品或道具描述：例如：手中的一叠美元钞票，一叠叠整齐的新衣服
视频提示词：[必须使用上述单角色或多角色专业模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

**分镜2**
分镜内容：[用中文描述这个分镜的场景和动作]
图片提示词：[严格按照上述模板格式输出]
视频提示词：[必须使用上述单角色或多角色专业模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

重要要求：
- 保持角色外观、服装、场景的一致性
- 每个角色必须包含详细的外观和服装描述
- 图片提示词要遵循上述专业格式，包含镜头类型、光线氛围、人物状态、场景细节等
- 视频提示词必须严格按照单图片激活模板生成，禁止使用旧格式：
  * 格式：[核心画面描述]，[主体动作/表情的细微变化]。[环境元素的动态效果]。[镜头运动方式]。[风格与画质]。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。
  * 示例：中景镜头，黄昏时分的柔和光线，傍晚，在印度垃圾填埋场，背景是垃圾堆和远处模糊的城市。普莉娅（20多岁，身材极其瘦削，颧骨突出，长发凌乱，面容肮脏，穿着一件褪色且打着补丁的棉布库尔蒂衫和简单的萨尔瓦裤）右手拿着一块披萨，微微向下倾斜，眼神复杂地看着流浪母猫（一只瘦弱的杂色流浪猫）和几只小猫（毛色各异，体型瘦小）围在普莉娅身边，更加靠近，抬头渴望地看着披萨。风格：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。[光照]：自然光照，柔和且均匀，微妙且真实的光影。色彩：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。画质：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。
- 【重要】视频提示词必须用中文，严禁使用英文单词，必须是完整的中文句子，描述微小而真实的动作，注重细节变化
- 请尽量生成更多分镜，不要过度简化或合并场景
- 继续编号直到故事完整结束（分镜3、分镜4...分镜N）
- 直接开始分析，不要询问或说明

现在请分析以下故事：
`;

// 首尾帧模式规则字符串
const GEMINI_RULES_DUAL_FRAME = `
你是一个专业的分镜师。请将以下故事内容拆解成分镜头序列。

**重要指导原则：**
- 请尽可能详细地拆解故事，每个重要场景、动作、对话都应该有独立的分镜
- 对于复杂的场景，请拆解成多个分镜（例如：人物表情变化、镜头切换、动作序列等）
- 目标是生成10-25个分镜，确保故事的完整性和流畅性
- 不要省略任何重要的故事节点

你的任务是根据我提供的**视频脚本内容**，将每一个场景（或一个场景内的多个关键瞬间）拆解成独立的图片，并为每张图片生成详细的、用于图像生成模型的提示词。

**视频提示词生成规则（重要）：**

请使用以下专业的首尾帧视频提示词模板：

**核心理念：首尾帧叙事**
- **起始画面**：基于第一张图片进行动态化，让静态画面活起来
- **过渡动作**：描述从第一张图片到第二张图片的过渡动作
- **结束画面**：基于第二张图片进行动态化，完成叙事闭环
- **情感氛围**：为整个视频设定情感基调

**通用结构：**
\`图片 [shot1] → 图片 [shot2]  
[前X秒：起始画面与动态描述] + [后Y秒：核心互动与过渡描述]。[整体情感与氛围描述]。[技术性指令]\`

**实用格式：**
\`图片 1 → 图片 2  
在前3秒，[起始画面描述]。[起始动态描述]。在后2秒，[核心动作描述]。[反应描述]。[镜头运动描述]。整个画面充满了[情感氛围描述]。视频画面连贯，流畅，符合现实运动规则，不要出现其他角色。\`

**各部分详解：**
1. **起始画面描述**：精准描述第一张图片的场景、人物、穿着、构图
2. **起始动态描述**：为第一张图片增加微小但合理的动作（如：眼神流转、手指轻敲、呼吸起伏）
3. **核心动作描述**：描述主动方发起的核心动作
4. **反应描述**：描绘被动方如何对这个动作做出反应
5. **镜头运动描述**：明确指示镜头的移动、推拉、摇移或焦点转移
6. **情感氛围描述**：用一句话高度概括这个场景的核心情感
7. **技术性指令**：固定的命令短语，用于提升视频质量

**时间分段要求：**
- 前X秒：基于第一张图片的动态化
- 后Y秒：从第一张图片到第二张图片的过渡
- 时间分段要自然流畅，符合现实运动规律
- 每个时间段内的动作要连贯，避免突兀的跳跃

**视频提示词要求：**
- 【强制要求】必须使用中文，严禁任何英文单词，必须是完整的中文句子
- 简洁明了
- 必须严格按照上述首尾帧模板生成
- 包含四个核心要素：起始画面动态化 + 核心互动过渡 + 情感氛围 + 技术性指令
- 注重微小而真实的动作，避免大幅度位移
- 让背景环境也具有动态效果
- 使用缓慢微妙的镜头运动
- 适合AI视频生成工具使用
- 结尾必须包含：视频画面连贯，流畅，符合现实运动规则，不要出现其他角色

对于每个分镜，请按照以下格式输出：

**分镜1**
分镜内容：[用中文描述这个分镜的场景和动作]
图片提示词：[严格按照以下模板格式输出，注意：请将所有方括号内的占位符替换为具体内容，不要保留方括号]：

镜头类型，光线条件，时间，在场景描述，背景是背景细节描述。

主要角色描述1（人物特征，服装描述）动作描述，表情描述。  
主要角色描述2（人物特征，服装描述）动作描述，表情描述。  
...（如有更多主要角色，继续添加）。

风格：追求极致的超写实主义照片风格。画面呈现出高度的真实感，如同顶级数码单反相机（例如佳能EOSR5或索尼Alpha1级别)搭配高质量定焦镜头（例如50mmf/1.2或85mmf/1.4)在专业布光或完美自然光条件下精心拍摄的照片。
光照：自然光照，柔和且均匀，微妙且真实的光影。
色彩：写实色调，自然色彩。逼真的色彩，准确的白平衡，避免偏黄色调。干净且平衡的色彩，不要过度饱和。
画质：画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。

**重要说明：**
- 请将上述模板中的所有 [占位符] 替换为具体内容，不要保留方括号
- 镜头类型：例如：全身镜头，中景镜头，特写镜头
- 光线条件：例如：在明亮的日光下，在柔和自然光线，天空朦胧
- 时间：例如：白天，夜晚，黄昏，清晨
- 场景描述：例如：一个繁华的印度城市街道，一间破旧的印度乡村房屋内部
- 背景细节描述：例如：背景是带有"SAVCINO"和"GRIANET"标志的奢侈品店橱窗
- 主要角色描述：包含角色名称、人物特征、服装描述、动作描述、表情描述
- 特定物品或道具描述：例如：手中的一叠美元钞票，一叠叠整齐的新衣服
视频提示词：[必须使用上述首尾帧模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

**分镜2**
分镜内容：[用中文描述这个分镜的场景和动作]
图片提示词：[严格按照上述模板格式输出]
视频提示词：[必须使用上述首尾帧模板生成动态视频提示词，中文，描述完整的动态过程和转场效果]
字幕：[中文对白或旁白]

重要要求：
- 保持角色外观、服装、场景的一致性
- 每个角色必须包含详细的外观和服装描述
- 图片提示词要遵循上述专业格式，包含镜头类型、光线氛围、人物状态、场景细节等
- 视频提示词必须严格按照首尾帧模板生成，格式为：图片 1 → 图片 2 + 时间分段描述 + 情感氛围 + 技术性指令
- 【重要】视频提示词必须用中文，严禁使用英文单词，必须是完整的中文句子，描述微小而真实的动作，注重细节变化
- 请尽量生成更多分镜，不要过度简化或合并场景
- 继续编号直到故事完整结束（分镜3、分镜4...分镜N）
- 直接开始分析，不要询问或说明

现在请分析以下故事：
`;

export default function VideoAnalysisPage() {
  const [story, setStory] = useState("")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisMode, setAnalysisMode] = useState("single-frame") // 默认首帧模式
  const { toast } = useToast()
  const router = useRouter()
  const { user } = useAuth()

  const handleAnalyze = async () => {
    if (!story.trim()) {
      toast({
        title: "错误",
        description: "请输入故事内容",
        variant: "destructive",
      })
      return
    }

    if (!user) {
      toast({
        title: "错误",
        description: "请先登录以保存分镜",
        variant: "destructive",
      })
      return
    }

    setIsAnalyzing(true)
    
    try {
      // 添加超时处理
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
        toast({
          title: "请求超时",
          description: "API 响应时间过长，请重试",
          variant: "destructive",
      })
      }, 35000)

      // 根据选择的模式使用不同的规则
      const rules = analysisMode === "dual-frame" ? GEMINI_RULES_DUAL_FRAME : GEMINI_RULES_SINGLE_FRAME

      const result = await fetch("/api/gemini", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          story: story,
          rules: rules
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!result.ok) {
        const errorData = await result.json()
        throw new Error(errorData.error || `HTTP ${result.status}`)
      }

      const data = await result.json()

      // 解析响应
      const shots = parseGeminiShots(data)
      
      if (shots.length === 0) {
        throw new Error("未能解析出分镜内容，请重试")
      }

      // 创建新项目
      const project = await db.createProject({
        user_id: user.id,
        title: `故事分镜 - ${new Date().toLocaleDateString()}`,
        description: story.substring(0, 100) + (story.length > 100 ? "..." : ""),
        status: "draft",
        data: { originalStory: story, analysisMode: analysisMode }
      })

      // 批量创建分镜数据
      const shotsToCreate = shots.map((shot, index) => ({
        project_id: project.id,
        shot_number: shot.index,
        title: `分镜 ${shot.index}`,
        content: shot.content,
        image_prompt: shot.imagePrompt,
        english_prompt: shot.videoPrompt,
        video_url: undefined,
        image_url: undefined,
        duration: 5,
        shot_type: "Medium Shot",
        notes: shot.subtitle || "",
        status: "draft" as const
      }))

      await db.createShotsBatch(shotsToCreate)

      toast({
        title: "分析完成",
        description: `成功生成 ${shots.length} 个分镜`,
      })

      // 跳转到分镜页面
      router.push(`/storyboards?project=${project.id}`)

    } catch (error: any) {
      console.error("分析失败:", error)
      
      if (error.name === 'AbortError') {
        toast({
          title: "请求超时",
          description: "API 响应时间过长，请重试",
          variant: "destructive",
        })
      } else {
        toast({
          title: "分析失败",
          description: error.message || "请重试",
          variant: "destructive",
        })
      }
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 解析 Gemini 返回的分镜信息
  function parseGeminiShots(apiResponse: any): GeminiShot[] {
    try {
      let text = ""
      if (apiResponse.choices && apiResponse.choices[0]?.message?.content) {
        text = apiResponse.choices[0].message.content
      } else if (apiResponse.candidates && apiResponse.candidates[0]?.content?.parts?.[0]?.text) {
        text = apiResponse.candidates[0].content.parts[0].text
      } else {
        return []
      }

      const shots: GeminiShot[] = []
      const lines = text.split('\n')
      let currentShot: Partial<GeminiShot> = {}
      let currentSection = ""
      let shotIndex = 1

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        
        if (line.match(/^\*\*分镜\s*\d+/i) || line.match(/^分镜\s*\d+/i) || line.match(/^\d+[\.\、]/)) {
          if (currentShot.content) {
            shots.push({
              index: shotIndex,
              content: currentShot.content || "",
              imagePrompt: currentShot.imagePrompt || "",
              videoPrompt: currentShot.videoPrompt || "",
              subtitle: currentShot.subtitle || ""
            })
            shotIndex++
          }
          
          currentShot = {}
          currentSection = ""
          continue
        }
        
        if (line.includes("分镜内容") || line.includes("内容")) {
          currentSection = "content"
          const content = line.split(/[:：]/)[1]?.trim()
          if (content) currentShot.content = content
        } else if (line.includes("图片提示词") || line.includes("图片提示")) {
          currentSection = "imagePrompt"
          const prompt = line.split(/[:：]/)[1]?.trim()
          if (prompt) currentShot.imagePrompt = prompt
        } else if (line.includes("视频提示词") || line.includes("视频提示")) {
          currentSection = "videoPrompt"
          const prompt = line.split(/[:：]/)[1]?.trim()
          if (prompt) currentShot.videoPrompt = prompt
        } else if (line.includes("字幕") || line.includes("subtitle")) {
          currentSection = "subtitle"
          const subtitle = line.split(/[:：]/)[1]?.trim()
          if (subtitle) currentShot.subtitle = subtitle
        } else {
          if (currentSection === "content") {
            currentShot.content = (currentShot.content || "") + " " + line.trim()
          } else if (currentSection === "imagePrompt") {
            currentShot.imagePrompt = (currentShot.imagePrompt || "") + " " + line.trim()
          } else if (currentSection === "videoPrompt") {
            currentShot.videoPrompt = (currentShot.videoPrompt || "") + " " + line.trim()
          } else if (currentSection === "subtitle") {
            currentShot.subtitle = (currentShot.subtitle || "") + " " + line.trim()
          }
        }
      }

      if (currentShot.content) {
        shots.push({
          index: shotIndex,
          content: currentShot.content || "",
          imagePrompt: currentShot.imagePrompt || "",
          videoPrompt: currentShot.videoPrompt || "",
          subtitle: currentShot.subtitle || ""
        })
      }

      return shots

    } catch (error) {
      console.error("解析分镜时出错:", error)
      return []
    }
  }

  return (
    <div className="min-h-full bg-gray-950 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">AI Story Analysis</h1>
          <p className="text-gray-400">Transform your story into professional shot sequences with AI</p>
        </div>

        {/* Story Input Section */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              AI Story Analysis
            </CardTitle>
            <CardDescription className="text-gray-400">
              Write your story or paste an existing script. Our AI will automatically break it down into professional shots.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Analysis Mode Selection */}
            <div className="space-y-2">
              <Label className="text-white">Analysis Mode</Label>
              <RadioGroup
                value={analysisMode}
                onValueChange={setAnalysisMode}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="single-frame" id="single-frame" />
                  <Label htmlFor="single-frame" className="text-white cursor-pointer">
                    首帧模式 - 单图片激活视频提示词
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dual-frame" id="dual-frame" />
                  <Label htmlFor="dual-frame" className="text-white cursor-pointer">
                    首尾帧模式 - 双图片叙事视频提示词
                  </Label>
                </div>
              </RadioGroup>
              <p className="text-sm text-gray-400 mt-2">
                {analysisMode === "single-frame" 
                  ? "基于单张图片生成动态视频，适合静态场景的动态化"
                  : "基于两张图片的对比生成叙事视频，适合场景转换和角色互动"
                }
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="story" className="text-white">
                Story Content
              </Label>
              <Textarea
                id="story"
                placeholder="输入你的故事内容..."
                value={story}
                onChange={(e) => setStory(e.target.value)}
                className="min-h-[200px] bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 resize-none"
                disabled={isAnalyzing}
              />
            </div>

            <div className="flex justify-center">
              <Button
                onClick={handleAnalyze}
                disabled={!story.trim() || isAnalyzing || !user}
                size="lg"
                className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-semibold px-8"
              >
                {isAnalyzing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5 mr-2" />
                    开始分镜分析
                  </>
                )}
              </Button>
            </div>

            {/* Processing Indicator */}
            {isAnalyzing && (
              <Card className="glass-card">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                      <span className="text-lg font-medium text-white">正在分析故事...</span>
                    </div>
                    <p className="text-center text-gray-400">
                      AI 正在将您的故事拆解为专业分镜，这通常需要 10-30 秒
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
