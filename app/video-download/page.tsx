"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Download, LinkIcon, CheckCircle, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function VideoDownloadPage() {
  const [url, setUrl] = useState("")
  const [isDownloading, setIsDownloading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [downloadHistory, setDownloadHistory] = useState<
    Array<{
      id: string
      url: string
      title: string
      status: "completed" | "failed"
      timestamp: Date
    }>
  >([])
  const { toast } = useToast()

  const handleDownload = async () => {
    if (!url.trim()) {
      toast({
        title: "错误",
        description: "请输入有效的URL",
        variant: "destructive",
      })
      return
    }

    setIsDownloading(true)
    setProgress(0)

    // Simulate download progress
    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          return 100
        }
        return prev + Math.random() * 15
      })
    }, 500)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // Add to history
      const newDownload = {
        id: Date.now().toString(),
        url,
        title: `来自 ${new URL(url).hostname} 的视频`,
        status: "completed" as const,
        timestamp: new Date(),
      }

      setDownloadHistory((prev) => [newDownload, ...prev])

      toast({
        title: "成功",
        description: "视频下载成功！",
      })

      setUrl("")
    } catch (error) {
      toast({
        title: "错误",
        description: "视频下载失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
      setProgress(0)
    }
  }

  return (
    <div className="min-h-full bg-gray-950 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">视频下载</h1>
          <p className="text-gray-400">从各种平台下载视频到您的项目库</p>
        </div>

        {/* Download Form */}
        <Card className="glass-card mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Download className="w-5 h-5" />
              下载视频
            </CardTitle>
            <CardDescription className="text-gray-400">输入视频URL将其下载到您的库中</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="https://example.com/video-url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  disabled={isDownloading}
                  className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500"
                />
              </div>
              <Button onClick={handleDownload} disabled={isDownloading || !url.trim()} className="min-w-[120px]">
                {isDownloading ? "下载中..." : "下载"}
              </Button>
            </div>

            {isDownloading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-400">
                  <span>下载中...</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Supported Platforms */}
        <Card className="glass-card mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <LinkIcon className="w-5 h-5" />
              支持的平台
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {["YouTube", "Bilibili", "抖音", "快手", "微博", "腾讯视频", "爱奇艺", "优酷"].map((platform) => (
                <div key={platform} className="flex items-center gap-2 p-3 bg-gray-800/30 rounded-lg">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-medium text-white">{platform}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Download History */}
        {downloadHistory.length > 0 && (
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-white">下载历史</CardTitle>
              <CardDescription className="text-gray-400">您最近的下载记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {downloadHistory.map((download) => (
                  <div key={download.id} className="flex items-center justify-between p-4 bg-gray-800/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      {download.status === "completed" ? (
                        <CheckCircle className="w-5 h-5 text-green-400" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-400" />
                      )}
                      <div>
                        <div className="font-medium text-white">{download.title}</div>
                        <div className="text-sm text-gray-400">{download.url}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-400">{download.timestamp.toLocaleString("zh-CN")}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
