# 🔧 WAN图生视频API修复总结

## 🐛 问题分析

根据API文档 (https://fal.ai/models/fal-ai/wan/v2.2-a14b/image-to-video/turbo/api) 和错误日志，发现两个关键问题：

### 1. URL协议问题
**错误信息：**
```
"Error downloading file from /images/shot-xxx.jpg: Request URL is missing an 'http://' or 'https://' protocol."
```

**问题原因：**
- 我们传递给WAN API的是相对路径 `/images/shot-xxx.jpg`
- WAN API需要完整的URL（包含协议和域名）

### 2. 环境变量访问问题
- `.env.local` 中定义的是 `wan_api_key`（小写）
- 代码中使用 `process.env.wan_api_key` 可能存在兼容性问题

## ✅ 修复方案

### 1. 修复 `/app/api/wan/video/route.ts`

**添加URL转换逻辑：**
```typescript
// 转换相对路径为完整URL
let fullImageUrl = image_url
if (image_url.startsWith('/')) {
  const host = request.headers.get('host')
  const protocol = request.headers.get('x-forwarded-proto') || 'http'
  fullImageUrl = `${protocol}://${host}${image_url}`
  console.log('转换相对路径为完整URL:', image_url, '->', fullImageUrl)
}
```

**优化环境变量访问：**
```typescript
const apiKey = process.env.wan_api_key || process.env.WAN_API_KEY
```

### 2. 修复 `/app/api/wan/status/route.ts`

**同样的环境变量优化：**
- 在GET和POST两个函数中都添加了兼容性处理

## 🎯 API要求对照

根据 [WAN API文档](https://fal.ai/models/fal-ai/wan/v2.2-a14b/image-to-video/turbo/api)：

### 输入要求：
- `image_url` (string)* required: "URL of the input image"
- `prompt` (string)* required: "The text prompt to guide video generation"

### 输出格式：
```json
{
  "video": {
    "url": "https://storage.googleapis.com/falserverless/gallery/wan-i2v-turbo.mp4"
  },
  "prompt": "...",
  "seed": 12345
}
```

## 🔄 预期效果

修复后的流程：
1. **相对路径** → **完整URL**：`/images/shot-xxx.jpg` → `http://localhost:3000/images/shot-xxx.jpg`
2. **API调用成功**：WAN API能够正确下载和处理图片
3. **视频生成**：返回生成的视频URL
4. **UI更新**：在分镜卡片中显示生成的视频

## 🚀 测试步骤

1. 确保有分镜包含图片和视频提示词
2. 点击紫色的"🎬 图生视频"按钮
3. 观察终端日志中的URL转换信息
4. 等待视频生成完成（30秒-3分钟）
5. 确认视频在UI中正确显示

## 📝 改动文件

- ✅ `app/api/wan/video/route.ts` - 添加URL转换和环境变量兼容性
- ✅ `app/api/wan/status/route.ts` - 添加环境变量兼容性