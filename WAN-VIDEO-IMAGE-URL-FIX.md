# 🔧 WAN视频生成图片URL修复

## 🐛 问题分析

从最新的错误日志发现：

### ✅ 视频生成成功
```
status: 'COMPLETED'
inference_time: 23.744308948516846
```

### ❌ 获取结果失败
```
Error downloading file from http://localhost:3000/images/shot-xxx.jpg: All connection attempts failed
```

**根本原因**：
- WAN API成功生成了视频
- 但在获取结果时，WAN服务器无法访问本地图片URL
- `http://localhost:3000/images/xxx.jpg` 对WAN服务器不可见

## 🔧 解决方案

### 1. 使用原始图片URL（公网可访问）

**修改逻辑**：
```typescript
// 优先使用原始图片URL（公网可访问），如果没有则使用本地URL
let videoImageUrl = imageUrl

// 查找当前分镜的原始图片URL
const currentShot = shots.find(s => s.id === shotId)
if (currentShot?.original_image_url && imageUrl.startsWith('/')) {
  videoImageUrl = currentShot.original_image_url
  console.log("🔄 使用原始图片URL（公网可访问）:", videoImageUrl)
} else {
  console.log("🔄 使用当前图片URL:", videoImageUrl)
}
```

### 2. 数据库字段支持

**Shot接口已包含**：
```typescript
export interface Shot {
  // ... 其他字段
  image_url?: string
  original_image_url?: string // 存储原始图片URL作为备份
  video_url?: string
  // ... 其他字段
}
```

### 3. 图片存储逻辑

**在`app/api/store-image/route.ts`中**：
```typescript
return NextResponse.json({
  success: true,
  localUrl: localUrl,        // 本地URL: /images/xxx.jpg
  originalUrl: imageUrl      // 原始URL: https://xxx.com/xxx.jpg
})
```

**在`app/storyboards/page.tsx`中保存**：
```typescript
await db.updateShot(shotId, {
  image_url: localImageUrl,           // 本地URL
  original_image_url: data.data[0].url, // 原始URL（公网可访问）
  // ... 其他字段
})
```

## 🎯 修复效果

### 修复前：
1. 图片生成 → 保存本地URL `/images/xxx.jpg`
2. 视频生成 → 使用本地URL
3. WAN API → 无法访问本地URL → 失败

### 修复后：
1. 图片生成 → 保存本地URL + 原始URL
2. 视频生成 → 优先使用原始URL（公网可访问）
3. WAN API → 成功访问图片 → 生成视频成功

## 📊 技术细节

### URL优先级：
1. **原始图片URL**（如果存在且当前是本地URL）
2. **当前图片URL**（作为备选）

### 兼容性处理：
- 如果`original_image_url`不存在，使用当前URL
- 如果当前URL已经是公网URL，直接使用
- 如果当前URL是本地URL，尝试使用原始URL

### 日志记录：
```
🔄 使用原始图片URL（公网可访问）: https://xxx.com/xxx.jpg
🔄 使用当前图片URL: /images/xxx.jpg
```

## 🚀 预期结果

修复后，WAN视频生成流程：
1. ✅ 提交请求（使用公网图片URL）
2. ✅ 轮询状态（队列→处理中→完成）
3. ✅ 获取结果（WAN服务器可访问图片）
4. ✅ 保存视频URL到数据库
5. ✅ 在UI中显示生成的视频

## 📝 测试建议

1. **确保有原始图片URL**：检查分镜是否包含`original_image_url`
2. **测试视频生成**：点击"🎬 图生视频"按钮
3. **观察日志**：确认使用了公网图片URL
4. **验证结果**：检查视频是否成功生成并显示

现在WAN视频生成应该能够正确处理图片URL问题了！🎬✨ 