export interface GeminiResponse {
  candidates?: Array<{
    content: { parts: Array<{ text: string }> };
  }>;
  choices?: Array<{
    message: { content: string };
  }>;
}

export async function analyzeStoryWithGemini(story: string, rules: string): Promise<string> {
  const res = await fetch("/api/gemini", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ story, rules }),
  });
  if (!res.ok) throw new Error("Gemini API 调用失败");
  const data: GeminiResponse = await res.json();
  
  // 处理 OpenRouter 格式
  if (data.choices && data.choices.length > 0) {
    return data.choices[0].message.content || "";
  }
  
  // 处理 Google Gemini 原生格式（备用）
  return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
} 