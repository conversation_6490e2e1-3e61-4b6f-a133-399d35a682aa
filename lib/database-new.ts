import { createClient } from "./supabase"

export interface Profile {
  id: string
  updated_at?: string
  username?: string
  full_name?: string
  avatar_url?: string
  website?: string
}

export interface Project {
  id: string
  name: string
  created_at: string
  updated_at: string
  user_id: string
  characters: any[]
  character_consistency_prompt?: string
}

export interface Storyboard {
  id: string
  project_id: string
  project_name: string
  order: number
  content: string
  image_prompt: string
  image_prompt_en: string
  video_prompt: string
  video_prompt_en: string
  subtitle: string
  subtitle_en: string
  images: any[]
  videos: any[]
  created_at: string
  updated_at: string
  user_id: string
}

export interface UserSettings {
  id: string
  user_id: string
  image_size: string
  image_model: string
  image_style: string
  created_at: string
  updated_at: string
}

export interface YouTubeContent {
  id: string
  title: string
  description: string
  tags: string
  project_name: string
  created_at: string
  updated_at: string
  user_id: string
}

export class DatabaseService {
  private supabase = createClient()

  // Profile operations
  async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await this.supabase.from("profiles").select("*").eq("id", userId).single()

    if (error) throw error
    return data
  }

  async updateProfile(userId: string, updates: Partial<Profile>): Promise<Profile> {
    const { data, error } = await this.supabase.from("profiles").update(updates).eq("id", userId).select().single()

    if (error) throw error
    return data
  }

  // Project operations
  async getProjects(userId: string): Promise<Project[]> {
    const { data, error } = await this.supabase
      .from("projects")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })

    if (error) throw error
    return data || []
  }

  async createProject(project: Omit<Project, "id" | "created_at" | "updated_at">): Promise<Project> {
    const { data, error } = await this.supabase.from("projects").insert(project).select().single()

    if (error) throw error
    return data
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    const { data, error } = await this.supabase.from("projects").update(updates).eq("id", id).select().single()

    if (error) throw error
    return data
  }

  async deleteProject(id: string): Promise<void> {
    const { error } = await this.supabase.from("projects").delete().eq("id", id)

    if (error) throw error
  }

  // Storyboard operations
  async getStoryboards(projectId: string): Promise<Storyboard[]> {
    const { data, error } = await this.supabase
      .from("storyboards")
      .select("*")
      .eq("project_id", projectId)
      .order("order", { ascending: true })

    if (error) throw error
    return data || []
  }

  async createStoryboard(storyboard: Omit<Storyboard, "id" | "created_at" | "updated_at">): Promise<Storyboard> {
    const { data, error } = await this.supabase.from("storyboards").insert(storyboard).select().single()

    if (error) throw error
    return data
  }

  async updateStoryboard(id: string, updates: Partial<Storyboard>): Promise<Storyboard> {
    const { data, error } = await this.supabase.from("storyboards").update(updates).eq("id", id).select().single()

    if (error) throw error
    return data
  }

  async deleteStoryboard(id: string): Promise<void> {
    const { error } = await this.supabase.from("storyboards").delete().eq("id", id)

    if (error) throw error
  }

  async reorderStoryboards(projectId: string, storyboardUpdates: Array<{ id: string; order: number }>): Promise<void> {
    const promises = storyboardUpdates.map(({ id, order }) =>
      this.supabase.from("storyboards").update({ order }).eq("id", id).eq("project_id", projectId),
    )

    const results = await Promise.all(promises)
    const errors = results.filter((result) => result.error)

    if (errors.length > 0) {
      throw errors[0].error
    }
  }

  // User Settings operations
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await this.supabase.from("user_settings").select("*").eq("user_id", userId).single()

    if (error) throw error
    return data
  }

  async updateUserSettings(userId: string, updates: Partial<UserSettings>): Promise<UserSettings> {
    const { data, error } = await this.supabase
      .from("user_settings")
      .update(updates)
      .eq("user_id", userId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // YouTube Content operations
  async getYouTubeContents(userId: string): Promise<YouTubeContent[]> {
    const { data, error } = await this.supabase
      .from("youtube_contents")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })

    if (error) throw error
    return data || []
  }

  async createYouTubeContent(
    content: Omit<YouTubeContent, "id" | "created_at" | "updated_at">,
  ): Promise<YouTubeContent> {
    const { data, error } = await this.supabase.from("youtube_contents").insert(content).select().single()

    if (error) throw error
    return data
  }
}

export const db = new DatabaseService()
