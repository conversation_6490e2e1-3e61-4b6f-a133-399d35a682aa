import { createClient } from "./supabase"

export interface User {
  id: string
  email: string
  name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Project {
  id: string
  user_id: string
  title: string
  description?: string
  status: "draft" | "in_progress" | "completed"
  data?: any
  created_at: string
  updated_at: string
}

export interface Video {
  id: string
  project_id: string
  title: string
  url?: string
  duration?: number
  metadata?: any
  created_at: string
}

export interface Shot {
  id: string
  project_id: string
  shot_number: number
  title: string
  content?: string
  image_prompt?: string
  english_prompt?: string
  image_url?: string
  original_image_url?: string // 存储原始图片URL作为备份
  video_url?: string
  runway_task_id?: string // RUNWAY API的任务ID
  duration?: number
  shot_type?: string
  notes?: string
  image_size?: string
  generation_model?: string
  image_style?: string
  generation_mode?: string
  status: "draft" | "generating" | "completed" | "failed"
  created_at: string
  updated_at: string
}

// Mock data for demo mode - 使用 localStorage 持久化
const getDemoProjects = (): Project[] => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('demo-projects')
    if (stored) {
      const projects = JSON.parse(stored)
      console.log("📦 从localStorage加载演示项目:", projects.length, "个")
      return projects
    }
  }
  
  // 获取当前用户ID，如果没有则使用默认ID
  let currentUserId = "550e8400-e29b-41d4-a716-446655440000"
  if (typeof window !== 'undefined') {
    const demoUser = localStorage.getItem('demo-user')
    if (demoUser) {
      try {
        const user = JSON.parse(demoUser)
        currentUserId = user.id
        console.log("👤 使用演示用户ID:", currentUserId)
      } catch (e) {
        console.log("❌ 解析演示用户数据失败:", e)
      }
    } else {
      console.log("⚠️ 未找到演示用户数据，使用默认ID")
    }
  }
  
  // 返回默认的演示项目，使用当前用户ID
  const defaultProjects = [
    {
      id: "550e8400-e29b-41d4-a716-************",
      user_id: currentUserId,
      title: "Luxury Cruise Adventure",
      description: "A romantic story on a luxury cruise ship",
      status: "in_progress" as const,
      data: { budget: 10000, deadline: "2024-03-01", target_audience: "romance enthusiasts" },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "550e8400-e29b-41d4-a716-446655440002",
      user_id: currentUserId,
      title: "Brand Story Video",
      description: "Telling our company story through video",
      status: "draft" as const,
      data: { style: "documentary", duration: "3-5 minutes" },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "550e8400-e29b-41d4-a716-************",
      user_id: currentUserId,
      title: "故事分镜",
      description: "印度家庭故事分镜脚本",
      status: "in_progress" as const,
      data: { genre: "drama", duration: "5-8 minutes", target_audience: "family" },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ]
  
  console.log("🎯 创建默认演示项目，用户ID:", currentUserId)
  return defaultProjects
}

const saveDemoProjects = (projects: Project[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('demo-projects', JSON.stringify(projects))
  }
}

const getDemoShots = (): Shot[] => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('demo-shots')
    if (stored) {
      return JSON.parse(stored)
    }
  }
  // 返回默认的演示分镜
  return [
  {
    id: "shot-1",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 1,
    title: "Opening Scene - Cruise Deck",
    content:
      "Anna lies on the luxurious cruise ship deck, enjoying the golden hour sun with Marquez taking photos of her.",
    image_prompt:
      "在豪华邮轮甲板上的黄金时段，面对红色夕阳和大海。**安娜**（丰满的印度女士，穿着优雅的白色连衣裙，金色头发，黑发扎起，温柔快乐的表情）**躺在甲板上，**马奎兹**（穿着休闲的印度服装，金色丝绸衬衫，黑头发，温柔的表情，拿着彩色手机）**正在给她拍照。场景：豪华邮轮甲板，木地板，金色栏杆，远处的大海。构图：中景，安娜躺在画面右侧，马奎兹在左侧拍照。光线：夕阳的多重金色反射在人们身上，形成温暖的光线，氛围：浪漫，温暖。",
    english_prompt:
      "On the luxurious cruise ship deck at golden hour, facing the red sunset and sea. **Anna** (chubby Indian lady, wearing elegant white dress, golden hair, black hair tied up, gentle and happy expression) **lies on the deck, **Marquez** (wearing casual Indian attire, golden silk shirt, black hair, gentle expression, holding a colored phone) **is taking photos of her. Scene: luxurious cruise deck, wooden floor, golden railings, distant sea. Composition: medium shot, Anna lying on the right side of the frame, Marquez on the left taking photos. Lighting: multiple golden sunset reflections on people, forming warm light, atmosphere: romantic, warm.",
    image_url: "/images/shot-shot-test-1753794221043.png", // 使用实际生成的图片
    duration: 15,
    shot_type: "Wide Shot",
    notes: "Use natural golden hour lighting for romantic atmosphere",
    image_size: "2:3", // 使用标准格式
    generation_model: "sdxl",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "completed",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "shot-2",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 2,
    title: "Character Close-up - Anna",
    content:
      "Close-up of Anna's peaceful face bathed in golden sunlight, showing her serene expression and gentle smile.",
    image_prompt:
      "安娜面部的特写镜头，在金色阳光下，显示她宁静的表情，温柔的微笑，眼睛微闭，温暖的阳光在她的脸部特征上创造柔和的光辉。背景虚化的邮轮甲板，重点突出她的面部表情。",
    english_prompt:
      "Close-up shot of Anna's face in golden hour lighting, peaceful expression, gentle smile, eyes gently closed, warm sunlight creating a soft glow on her facial features. Blurred cruise deck background, focus on her facial expression.",
    image_url: "/images/shot-e9d6e64d-012f-4e7a-9597-17ab25b7173f-1753794378115.png", // 使用实际生成的图片
    duration: 10,
    shot_type: "Close-up",
    notes: "Focus on emotional expression and lighting",
    image_size: "9:16", // 使用标准格式
    generation_model: "sdxl",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "completed",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "shot-3",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 3,
    title: "Marquez Photography Moment",
    content:
      "Medium shot of Marquez holding his phone, capturing the perfect moment with concentration and artistic vision.",
    image_prompt:
      "马奎兹的中景镜头，他专注地举着手机拍照，展现他的艺术眼光和专注表情。穿着金色丝绸衬衫，黑发在微风中轻拂，邮轮的豪华环境作为背景，展现他作为摄影师的专业态度。",
    english_prompt:
      "Medium shot of Marquez holding his phone with concentration, showing his artistic vision and focused expression. Wearing golden silk shirt, black hair gently flowing in the breeze, luxury cruise environment as background, displaying his professional attitude as a photographer.",
    image_url: "/placeholder.svg?height=400&width=600&text=Marquez+Photography",
    duration: 10,
    shot_type: "Medium Shot",
    notes: "Capture the photographer's perspective and concentration",
    image_size: "portrait-2-3",
    generation_model: "sdxl",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "completed",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "shot-4",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 4,
    title: "Romantic Interaction",
    content:
      "Two-shot showing both Anna and Marquez sharing a tender moment as the sun sets behind them, creating silhouettes.",
    image_prompt:
      "双人镜头显示安娜和马奎兹分享温柔时刻，夕阳在他们身后落下，创造出美丽的剪影效果。他们面对面站着，手轻触，背景是壮观的海洋日落，整个画面充满浪漫氛围。",
    english_prompt:
      "Two-shot showing Anna and Marquez sharing a tender moment, sunset falling behind them, creating beautiful silhouette effects. They stand face to face, hands gently touching, with spectacular ocean sunset as background, the entire frame filled with romantic atmosphere.",
    image_url: "/placeholder.svg?height=400&width=600&text=Romantic+Silhouette",
    duration: 12,
    shot_type: "Two Shot",
    notes: "Create dramatic silhouette effect with sunset backlighting",
    image_size: "landscape-16-9",
    generation_model: "sdxl",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "shot-5",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 5,
    title: "Cruise Ship Establishing Shot",
    content:
      "Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky.",
    image_prompt:
      "整艘豪华邮轮的大远景镜头，在美丽的夕阳天空下航行穿过平静的海水。展现邮轮的宏伟规模和海洋的广阔，天空中有柔和的云彩，海面反射着夕阳的光芒，营造出宏大而宁静的氛围。",
    english_prompt:
      "Wide establishing shot of the entire luxury cruise ship sailing through calm ocean waters under a beautiful sunset sky. Showing the magnificent scale of the cruise ship and the vastness of the ocean, with soft clouds in the sky, sea surface reflecting sunset light, creating a grand and peaceful atmosphere.",
    image_url: "/placeholder.svg?height=400&width=600&text=Cruise+Ship+Wide",
    duration: 18,
    shot_type: "Extreme Wide Shot",
    notes: "Establish the grand scale and romantic setting",
    image_size: "landscape-16-9",
    generation_model: "sdxl",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  // 故事分镜项目的分镜
  {
    id: "story-shot-1",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 1,
    title: "清晨的家庭场景",
    content: "清晨,阳光照亮土坯房门口,安嘉莉为拉维整理领口,拉詹注视,普莉娅羡慕。",
    image_prompt: "[电影感镜头,柔和的晨光,印度乡村一间破旧的土坯房门口。安嘉莉(Anjali),38岁,印度妇女,面容忧虑,穿着褪色的蓝色棉布纱丽,正在为儿子拉维(Ravi)整理领口。拉维,9岁,印度男孩,穿着整洁的白色衬衫和卡其色短裤校服,背着棕色书包。拉詹(Rajan),40岁,印度男人,身材瘦削,黑色胡须,面容严肃,穿着磨损的棕色长袖衬衫和深色长裤,站在门内。普莉娅(Priya),8岁,印度女孩,扎马尾辫,眼神渴望,穿着破旧的黄色连衣裙式上衣和配套的旧裤子,站在一旁。背景是简陋的土坯房,细节丰富,电影质感,4K画质。]",
    english_prompt: "[Cinematic shot, soft morning light, at the doorway of a dilapidated adobe house in an Indian village. Anjali, 38, Indian woman, with a worried expression, wearing a faded blue cotton sari, is tidying Ravi's collar. Ravi, 9, Indian boy, wearing a neat white shirt and khaki shorts school uniform, carrying a brown school bag. Rajan, 40, Indian man, thin build, black beard, serious expression, wearing a worn brown long-sleeved shirt and dark trousers, standing inside the doorway. Priya, 8, Indian girl, with pigtails, longing eyes, wearing a worn yellow dress-style top and matching old pants, standing nearby. The background is a simple adobe house, rich in detail, cinematic quality, 4K resolution.]",
    image_url: undefined,
    duration: 12,
    shot_type: "Wide Shot",
    notes: "Establish the family dynamics and setting",
    image_size: "9:16",
    generation_model: "flux",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "story-shot-2",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 2,
    title: "普莉娅的内心世界",
    content: "普莉娅独自在房间角落,眼神中透露着对知识的渴望和对未来的憧憬。",
    image_prompt: "[中景镜头,光线昏暗,印度乡村破旧的房间内,夜晚。普莉娅（Priya）,8岁,印度女孩,穿着破旧的黄色连衣裙式上衣,坐在自己的小床上,伤心地哭泣。片刻后,她擦干眼泪,眼神变得坚定,背上一个简单的布包,决定离开家。超高清,细节丰富,电影质感,照片级真实感,电影级光效,4K画质。]",
    english_prompt: "[Medium shot, dim lighting, inside a rundown room in an Indian village, night. Priya, 8 years old, Indian girl, wearing a worn yellow dress-style top, sitting on her small bed, crying sadly. After a moment, she wipes away her tears, her eyes become determined, and she puts on a simple cloth bag, deciding to leave home. Ultra HD, rich details, cinematic quality, photorealistic, cinematic lighting, 4K resolution.]",
    image_url: undefined,
    duration: 8,
    shot_type: "Medium Shot",
    notes: "Show Priya's emotional journey and determination",
    image_size: "9:16",
    generation_model: "flux",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "story-shot-3",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 3,
    title: "火车站的决定",
    content: "普莉娅在拥挤的火车站台,眼神坚定地挤上火车,开始她的新生活。",
    image_prompt: "[广角镜头,夜晚,拥挤的印度火车站台。普莉娅（Priya）,10岁,印度女孩,眼神坚定,穿着那件破旧的黄色连衣裙式上衣,背着布包,在拥挤的人群中,努力地挤上一列即将开动的火车。超高清,细节丰富,电影质感,照片级真实感,电影级光效,4K画质。]",
    english_prompt: "[Wide-angle shot, night, crowded Indian train station platform. Priya, 10 years old, Indian girl, determined eyes, wearing that worn yellow dress-style top, carrying a cloth bag, struggling to squeeze onto a train that is about to depart among the crowded crowd. Ultra HD, rich details, cinematic quality, photorealistic, cinematic lighting, 4K resolution.]",
    image_url: undefined,
    duration: 15,
    shot_type: "Wide Shot",
    notes: "Capture the moment of departure and new beginning",
    image_size: "9:16",
    generation_model: "flux",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "story-shot-4",
    project_id: "550e8400-e29b-41d4-a716-************",
    shot_number: 4,
    title: "幸福的家庭重聚",
    content: "普莉娅、迈克尔和宝宝幸福地坐在一起,展现温馨的家庭氛围。",
    image_prompt: "[广角镜头,白天,宽敞明亮的豪宅客厅。普莉娅(Priya),28岁,印度成功女性,穿着华丽的深红色纱丽,她的丈夫迈克尔(Michael),30岁,白人男性,穿着米色休闲西装,和他们可爱的宝宝幸福地坐在一起,微笑着向镜头挥手。超高清,细节丰富,电影质感,照片级真实感,电影级光效,4K画质。]",
    english_prompt: "[Wide-angle shot, daytime, spacious and bright luxurious living room. Priya (Priya), 28 years old, successful Indian woman, wearing a gorgeous dark red sari, and her husband Michael (Michael), 30 years old, white male, wearing a beige casual suit, and their cute baby are sitting happily together, smiling and waving at the camera. Ultra HD, rich details, cinematic quality, photographic realism, cinematic lighting, 4K resolution.]",
    image_url: undefined,
    duration: 10,
    shot_type: "Wide Shot",
    notes: "Show the happy ending and family reunion",
    image_size: "9:16",
    generation_model: "flux",
    image_style: "realistic-photography",
    generation_mode: "smart",
    status: "draft",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  ]
}

const saveDemoShots = (shots: Shot[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('demo-shots', JSON.stringify(shots))
  }
}

export class DatabaseService {
  private supabase = createClient()

  // Check if we're in demo mode
  private isDemoMode(): boolean {
    // 检查是否配置了真实的 Supabase 环境变量
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    
    console.log("🔍 检查演示模式 - URL:", supabaseUrl ? "已配置" : "未配置")
    console.log("🔍 检查演示模式 - KEY:", supabaseKey ? "已配置" : "未配置")
    
    // 如果没有配置环境变量或者是占位符值，则使用演示模式
    if (!supabaseUrl || !supabaseKey || 
        supabaseUrl === "https://placeholder.supabase.co" ||
        supabaseKey === "public-anon-key" ||
        supabaseUrl.includes("your_supabase") ||
        supabaseKey.includes("your_supabase")) {
      console.log("🎭 使用演示模式：Supabase 环境变量未正确配置")
      return true
    }
    
    console.log("🗄️ 使用真实数据库：Supabase 已配置")
    console.log("🔗 Supabase URL:", supabaseUrl)
    return false
  }

  // User operations
  async getUser(id: string): Promise<User | null> {
    if (this.isDemoMode()) {
      return {
        id: "550e8400-e29b-41d4-a716-446655440000",
        email: "<EMAIL>",
        name: "Demo User",
        avatar_url: undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    }

    const { data, error } = await this.supabase.from("users").select("*").eq("id", id).single()

    if (error) throw error
    return data as User | null
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    if (this.isDemoMode()) {
      throw new Error("Cannot update user in demo mode")
    }

    const { data, error } = await this.supabase
      .from("users")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single()

    if (error) throw error
    return data as User
  }

  // Project operations
  async getProjects(userId: string): Promise<Project[]> {
    // 用户ID验证和修复
    console.log("🔍 getProjects调用 - 原始用户ID:", userId, "长度:", userId?.length)
    console.log("🔍 用户ID类型:", typeof userId)
    
    if (!userId) {
      console.error("❌ 用户ID为空或undefined")
      throw new Error("User ID is required")
    }
    
    // 检查UUID格式是否正确（36个字符，包含4个破折号）
    if (userId.length !== 36 || !userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.warn("⚠️ 用户ID格式不正确，长度:", userId.length)
      
      // 如果是35个字符且缺少最后一位，尝试修复
      if (userId.length === 35 && userId.startsWith("ee95585c-45a8-434a-a797-f939166275")) {
        const correctedId = "ee95585c-45a8-434a-a797-f93916627540"
        console.log("🔧 自动修复用户ID:", correctedId)
        userId = correctedId
      } else {
        console.error("❌ 无法修复用户ID格式，原始ID:", userId)
        // 不抛出错误，而是尝试使用演示模式
        console.log("🎭 用户ID格式错误，强制进入演示模式")
        // 返回演示项目数据
        const demoProjects = getDemoProjects()
        console.log("📊 返回演示项目数据:", demoProjects.length, "个")
        return demoProjects
      }
    }
    
    console.log("✅ 使用用户ID:", userId)

    const demoMode = this.isDemoMode()
    console.log("🔍 演示模式状态:", demoMode)
    
    if (demoMode) {
      console.log("🎭 Demo mode: returning mock projects")
      console.log("🔍 请求的用户ID:", userId)
      console.log("🔍 演示项目数据 (过滤前):", getDemoProjects().map(p => ({id: p.id, title: p.title, user_id: p.user_id})))
      
      const userProjects = getDemoProjects()
        .filter((project) => {
          const matches = project.user_id === userId
          console.log(`🔍 项目 ${project.id} (${project.title}): user_id=${project.user_id}, 匹配=${matches}`)
          return matches
        })
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()) // 按创建时间倒序，最新的在前
      
      console.log("📊 过滤后的用户项目列表:", userProjects.map(p => ({id: p.id, title: p.title, created: p.created_at})))
      return userProjects
    }

    // 真实数据库模式，但如果没有数据，为用户创建演示数据
    const { data, error } = await this.supabase
      .from("projects")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("❌ 数据库查询错误:", error)
      throw error
    }

    // 如果没有项目数据，为用户创建演示项目
    if (!data || data.length === 0) {
      console.log("📝 用户没有项目，创建演示项目")
      
      // 首先确保用户在users表中存在
      await this.ensureUserExists(userId)
      
      const demoProjects = [
        {
          user_id: userId,
          title: "Luxury Cruise Adventure",
          description: "A romantic story on a luxury cruise ship",
          status: "in_progress" as const,
          data: { budget: 10000, deadline: "2024-03-01", target_audience: "romance enthusiasts" },
        },
        {
          user_id: userId,
          title: "Brand Story Video",
          description: "Telling our company story through video",
          status: "draft" as const,
          data: { style: "documentary", duration: "3-5 minutes" },
        }
      ]

      const createdProjects = []
      let hasCreationAttempted = false
      
      for (const project of demoProjects) {
        try {
          hasCreationAttempted = true
          const { data: newProject, error: createError } = await this.supabase
            .from("projects")
            .insert(project)
            .select()
            .single()
          
          if (createError) {
            console.error("❌ 创建演示项目详细错误:", createError)
            console.error("❌ 错误代码:", createError.code)
            console.error("❌ 错误消息:", createError.message)
            
            // 如果是重复插入错误(409)，查询现有项目而不是继续尝试创建
            if (createError.code === '23505' || 
                createError.message?.includes('duplicate key') ||
                createError.message?.includes('already exists') ||
                createError.code === 'PGRST116') {
              console.log("⚠️ 检测到重复项目或约束冲突，查询现有项目")
              // 重新查询已存在的项目
              const { data: existingData, error: existingError } = await this.supabase
                .from("projects")
                .select("*")
                .eq("user_id", userId)
                .order("created_at", { ascending: false })
              
              if (!existingError && existingData && existingData.length > 0) {
                console.log("✅ 找到现有项目:", existingData.length, "个")
                return existingData as Project[]
              }
              
              console.log("⚠️ 没有找到现有项目，停止尝试创建")
              return []
            } else {
              console.error("❌ 创建演示项目失败:", createError)
            }
          } else {
            createdProjects.push(newProject)
            console.log("✅ 创建演示项目:", newProject.title)
          }
        } catch (err) {
          console.error("❌ 创建项目时出错:", err)
        }
      }

      // 如果创建了一些项目，返回创建的项目
      if (createdProjects.length > 0) {
        console.log("📊 成功创建项目:", createdProjects.length, "个")
        return createdProjects
      }
      
      // 如果没有创建成功任何项目，但已经尝试过创建，返回空数组并停止重试
      if (hasCreationAttempted) {
        console.log("⚠️ 创建项目失败，但停止重试，返回空项目列表")
        return []
      }
    }

    console.log("📊 从数据库获取到项目:", data.length, "个")
    return data as Project[] || []
  }

  async createProject(projectData: Omit<Project, "id" | "created_at" | "updated_at">): Promise<Project> {
    // 用户ID验证和修复
    let { user_id, ...restData } = projectData
    console.log("🔍 创建项目 - 原始用户ID:", user_id, "长度:", user_id?.length)
    
    if (user_id && (user_id.length !== 36 || !user_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i))) {
      console.warn("⚠️ 创建项目 - 用户ID格式不正确，长度:", user_id.length)
      
      if (user_id.length === 35 && user_id.startsWith("ee95585c-45a8-434a-a797-f939166275")) {
        user_id = "ee95585c-45a8-434a-a797-f93916627540"
        console.log("🔧 创建项目 - 自动修复用户ID:", user_id)
      }
    }
    
    const finalProjectData = { user_id, ...restData }
    console.log("✅ 创建项目 - 使用用户ID:", user_id)

    if (this.isDemoMode()) {
      console.log("🎭 演示模式：开始创建项目")
      const newProject: Project = {
        id: `demo-project-${Date.now()}`,
        ...finalProjectData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      
      // 获取现有项目列表并添加新项目
      const existingProjects = getDemoProjects()
      const updatedProjects = [...existingProjects, newProject]
      saveDemoProjects(updatedProjects)
      
      console.log("✅ 演示模式：创建的项目已添加到数组", newProject)
      console.log("📊 当前项目总数:", updatedProjects.length)
      return newProject
    }

    const { data, error } = await this.supabase.from("projects").insert(finalProjectData).select().single()

    if (error) throw error
    return data as Project
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    if (this.isDemoMode()) {
      throw new Error("Cannot update project in demo mode")
    }

    const { data, error } = await this.supabase
      .from("projects")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteProject(id: string): Promise<void> {
    if (this.isDemoMode()) {
      // 演示模式下从localStorage删除项目
      const projects = getDemoProjects()
      const updatedProjects = projects.filter(p => p.id !== id)
      saveDemoProjects(updatedProjects)
      
      // 同时删除相关的分镜数据
      const shots = getDemoShots()
      const updatedShots = shots.filter(s => s.project_id !== id)
      saveDemoShots(updatedShots)
      
      console.log(`🗑️ 演示模式：删除项目 ${id} 及相关分镜`)
      return
    }

    const { error } = await this.supabase.from("projects").delete().eq("id", id)

    if (error) throw error
  }

  // Video operations
  async getVideos(projectId: string): Promise<Video[]> {
    if (this.isDemoMode()) {
      return []
    }

    const { data, error } = await this.supabase
      .from("videos")
      .select("*")
      .eq("project_id", projectId)
      .order("created_at", { ascending: false })

    if (error) throw error
    return data as Video[] || []
  }

  async createVideo(video: Omit<Video, "id" | "created_at">): Promise<Video> {
    if (this.isDemoMode()) {
      throw new Error("Cannot create video in demo mode")
    }

    const { data, error } = await this.supabase.from("videos").insert(video).select().single()

    if (error) throw error
    return data as Video
  }

  async deleteVideo(id: string): Promise<void> {
    if (this.isDemoMode()) {
      throw new Error("Cannot delete video in demo mode")
    }

    const { error } = await this.supabase.from("videos").delete().eq("id", id)

    if (error) throw error
  }

  // Shot operations
  async getShots(projectId: string): Promise<Shot[]> {
    if (this.isDemoMode()) {
      console.log("Demo mode: returning mock shots for project", projectId)
      return getDemoShots()
        .filter((shot) => shot.project_id === projectId)
        .sort((a, b) => a.shot_number - b.shot_number) // 按分镜号排序
    }

    const { data, error } = await this.supabase
      .from("shots")
      .select("*")
      .eq("project_id", projectId)
      .order("shot_number", { ascending: true })

    if (error) {
      console.error("❌ 获取分镜数据错误:", error)
      throw error
    }

    // 如果没有分镜数据，为项目创建演示分镜
    if (!data || data.length === 0) {
      console.log("📝 项目没有分镜，创建演示分镜")
      
      // 获取项目信息以确定创建什么类型的分镜
      const { data: projectData } = await this.supabase
        .from("projects")
        .select("title")
        .eq("id", projectId)
        .single()

      const demoShots = [
        {
          project_id: projectId,
          shot_number: 1,
          title: "Opening Scene",
          content: "An engaging opening scene that captures the viewer's attention.",
          image_prompt: "Professional opening scene with dynamic composition and lighting",
          english_prompt: "Professional opening scene with dynamic composition and lighting",
          duration: 10,
          shot_type: "Wide Shot",
          notes: "Focus on establishing the scene and mood",
          image_size: "portrait-2-3",
          generation_model: "sdxl",
          image_style: "realistic-photography",
          generation_mode: "smart",
          status: "draft" as const,
        },
        {
          project_id: projectId,
          shot_number: 2,
          title: "Main Content",
          content: "The main content of the video that delivers the key message.",
          image_prompt: "Clear and engaging main content scene",
          english_prompt: "Clear and engaging main content scene",
          duration: 15,
          shot_type: "Medium Shot",
          notes: "Ensure clear communication of the main message",
          image_size: "portrait-2-3",
          generation_model: "sdxl",
          image_style: "realistic-photography",
          generation_mode: "smart",
          status: "draft" as const,
        }
      ]

      const createdShots = []
      for (const shot of demoShots) {
        try {
          const { data: newShot, error: createError } = await this.supabase
            .from("shots")
            .insert(shot)
            .select()
            .single()
          
          if (createError) {
            console.error("❌ 创建演示分镜失败:", createError)
          } else {
            createdShots.push(newShot)
            console.log("✅ 创建演示分镜:", newShot.title)
          }
        } catch (err) {
          console.error("❌ 创建分镜时出错:", err)
        }
      }

      return createdShots
    }

    console.log("📊 从数据库获取到分镜:", data.length, "个")
    return data as Shot[] || []
  }

  async createShot(shot: Omit<Shot, "id" | "created_at" | "updated_at">): Promise<Shot> {
    if (this.isDemoMode()) {
      console.log("🎬 演示模式：开始创建分镜", shot.shot_number)
      const newShot: Shot = {
        id: `demo-shot-${Date.now()}`,
        ...shot,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      // 添加到演示数据数组中
      saveDemoShots([...getDemoShots(), newShot]) // 改为 push，保持创建顺序
      console.log("✅ 演示模式：创建的镜头已添加到数组", newShot.shot_number, newShot.title)
      console.log("📊 当前分镜总数:", getDemoShots().length)
      return newShot
    }

    const { data, error } = await this.supabase.from("shots").insert(shot).select().single()

    if (error) throw error
    return data as Shot
  }

  // 批量创建分镜
  async createShotsBatch(shots: Omit<Shot, "id" | "created_at" | "updated_at">[]): Promise<Shot[]> {
    if (this.isDemoMode()) {
      console.log("🎭 演示模式：批量创建分镜")
      const createdShots: Shot[] = []
      for (const shot of shots) {
        const newShot: Shot = {
          ...shot,
          id: crypto.randomUUID(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
        createdShots.push(newShot)
      }
      return createdShots
    }

    console.log(`📦 批量创建 ${shots.length} 个分镜`)
    const { data, error } = await this.supabase
      .from("shots")
      .insert(shots)
      .select()

    if (error) {
      console.error("❌ 批量创建分镜失败:", error)
      throw error
    }

    console.log(`✅ 批量创建分镜成功: ${data.length} 个`)
    return data as Shot[]
  }

  async updateShot(id: string, updates: Partial<Shot>): Promise<Shot> {
    if (this.isDemoMode()) {
      console.log("🎭 演示模式：更新分镜", { shotId: id, updates })
      
      // Find and update the shot in demo data
      const shotIndex = getDemoShots().findIndex((shot) => shot.id === id)
      console.log("🔍 查找分镜索引:", shotIndex, "总数:", getDemoShots().length)
      
      if (shotIndex !== -1) {
        const updatedShots = [...getDemoShots()]
        const oldShot = updatedShots[shotIndex]
        updatedShots[shotIndex] = { ...oldShot, ...updates, updated_at: new Date().toISOString() }
        
        console.log("🔄 更新前:", { 
          id: oldShot.id, 
          image_url: oldShot.image_url?.substring(0, 50) + "...",
          video_url: oldShot.video_url?.substring(0, 50) + "..." 
        })
        console.log("🔄 更新后:", { 
          id: updatedShots[shotIndex].id, 
          image_url: updatedShots[shotIndex].image_url?.substring(0, 50) + "...",
          video_url: updatedShots[shotIndex].video_url?.substring(0, 50) + "..."
        })
        
        saveDemoShots(updatedShots)
        console.log("💾 已保存到localStorage，验证保存结果...")
        
        // 验证保存是否成功
        const verifyShots = getDemoShots()
        const verifyShot = verifyShots.find(s => s.id === id)
        console.log("✅ 验证保存结果:", verifyShot ? { 
          id: verifyShot.id, 
          image_url: verifyShot.image_url ? "已设置" : "未设置",
          video_url: verifyShot.video_url ? "已设置" : "未设置"
        } : "未找到")
        
        return updatedShots[shotIndex]
      }
      throw new Error("Shot not found in demo mode")
    }

    // 临时解决方案：过滤掉runway_task_id字段，直到数据库表结构更新完成
    const { runway_task_id, ...safeUpdates } = updates as any
    
    console.log("🔄 更新分镜，过滤runway_task_id:", { 
      shotId: id, 
      hasRunwayTaskId: !!runway_task_id,
      runwayTaskId: runway_task_id,
      otherUpdates: Object.keys(safeUpdates)
    })

    const { data, error } = await this.supabase
      .from("shots")
      .update({ ...safeUpdates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("❌ 更新分镜失败:", error)
      throw error
    }
    
    console.log("✅ 分镜更新成功")
    return data
  }

  async deleteShot(id: string): Promise<void> {
    if (this.isDemoMode()) {
      throw new Error("Cannot delete shot in demo mode")
    }

    const { error } = await this.supabase.from("shots").delete().eq("id", id)

    if (error) throw error
  }

  async reorderShots(projectId: string, shotUpdates: Array<{ id: string; shot_number: number }>): Promise<void> {
    if (this.isDemoMode()) {
      throw new Error("Cannot reorder shots in demo mode")
    }

    const { error } = await this.supabase.rpc("update_shot_numbers", {
      project_id: projectId,
      shot_updates: shotUpdates,
    })

    if (error) throw error
  }

  async duplicateShot(shotId: string, newShotNumber: number): Promise<Shot> {
    if (this.isDemoMode()) {
      throw new Error("Cannot duplicate shot in demo mode")
    }

    // First get the original shot
    const { data: originalShot, error: fetchError } = await this.supabase
      .from("shots")
      .select("*")
      .eq("id", shotId)
      .single()

    if (fetchError) throw fetchError

    // Create new shot with incremented shot_number
    const newShot = {
      ...originalShot,
      shot_number: newShotNumber,
      title: `${originalShot.title} (Copy)`,
      status: "draft" as const,
      image_url: null,
      video_url: null,
    }

    delete newShot.id
    delete newShot.created_at
    delete newShot.updated_at

    const { data, error } = await this.supabase.from("shots").insert(newShot).select().single()

    if (error) throw error
    return data as Shot
  }

  async generateShotImage(shotId: string): Promise<Shot> {
    if (this.isDemoMode()) {
      // Simulate image generation in demo mode
      const shotIndex = getDemoShots().findIndex((shot) => shot.id === shotId)
      if (shotIndex !== -1) {
        const updatedShots = [...getDemoShots()]
        updatedShots[shotIndex].status = "generating"
        saveDemoShots(updatedShots)

        // Simulate generation delay
        setTimeout(() => {
          const completedShots = [...updatedShots]
          completedShots[shotIndex].status = "completed"
          completedShots[shotIndex].image_url = `/placeholder.svg?height=400&width=600&text=Generated+${shotId.slice(-4)}`
          saveDemoShots(completedShots)
        }, 3000)

        return updatedShots[shotIndex]
      }
      throw new Error("Shot not found in demo mode")
    }

    // In a real implementation, this would call an AI image generation API
    // For now, we'll just update the status to generating and then completed
    const { data, error } = await this.supabase
      .from("shots")
      .update({
        status: "generating",
        updated_at: new Date().toISOString(),
      })
      .eq("id", shotId)
      .select()
      .single()

    if (error) throw error

    // Simulate image generation delay
    setTimeout(async () => {
      await this.supabase
        .from("shots")
        .update({
          status: "completed",
          image_url: `/placeholder.svg?height=400&width=600&text=Generated+Image+${shotId.slice(-4)}`,
          updated_at: new Date().toISOString(),
        })
        .eq("id", shotId)
    }, 3000)

    return data as Shot
  }

  private async ensureUserExists(userId: string): Promise<void> {
    try {
      // 先检查用户是否存在
      const { data: existingUser, error: checkError } = await this.supabase
        .from("users")
        .select("id")
        .eq("id", userId)
        .single()

      if (existingUser) {
        console.log(`✅ 用户 ${userId} 已存在`)
        return
      }

      // 用户不存在，创建新用户
      if (checkError && checkError.code === 'PGRST116') { // No rows found
        console.log(`👤 用户 ${userId} 不存在，尝试创建...`)
        
        // 从 auth.users 获取用户信息
        const { data: authUser, error: authError } = await this.supabase.auth.getUser()
        if (authError || !authUser.user) {
          console.error("❌ 无法获取当前用户信息:", authError)
          throw new Error("无法获取用户信息")
        }
        
        const userEmail = authUser.user.email || '<EMAIL>'
        const userName = authUser.user.user_metadata?.name || 
                        authUser.user.user_metadata?.full_name || 
                        userEmail.split('@')[0]
        
        console.log(`📧 创建用户记录: ${userName} (${userEmail})`)
        
        // 使用 ON CONFLICT DO NOTHING 避免重复插入错误
        const { data: newUser, error: createError } = await this.supabase
          .from("users")
          .insert({ 
            id: userId,
            email: userEmail,
            name: userName,
            avatar_url: authUser.user.user_metadata?.avatar_url
          })
          .select()
          
        if (createError && createError.code !== '23505') { // 23505 是重复键错误，可以忽略
          console.error("❌ 创建用户失败:", createError)
          throw createError
        }
        console.log(`✅ 用户 ${userId} 已创建`)
      } else {
        console.error("❌ 检查用户存在性失败:", checkError)
        throw checkError
      }
    } catch (error) {
      console.error("❌ ensureUserExists 执行失败:", error)
      // 不抛出错误，允许程序继续执行
      console.log("⚠️ 继续执行，忽略用户创建错误")
    }
  }
}

export const db = new DatabaseService()
