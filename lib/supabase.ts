// lib/supabase.ts
import { createClient as _createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// 创建单一的Supabase客户端实例，避免Multiple GoTrueClient instances警告
export const supabase = _createClient(supabaseUrl, supabaseAnonKey)

// 为了兼容性，导出createClient函数但返回同一个实例
export function createClient() {
  return supabase // 返回同一个实例而不是创建新的
}
