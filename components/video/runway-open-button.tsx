"use client"

import { But<PERSON> } from "@/components/ui/button"

interface RunwayOpenButtonProps {
  imageUrl?: string | null
  videoPrompt?: string | null
  size?: "sm" | "default"
  className?: string
}

export function OpenInRunwayButton({ imageUrl, videoPrompt, size = "sm", className }: RunwayOpenButtonProps) {
  async function openRunway() {
    const img = (imageUrl || "").trim()
    const prompt = (videoPrompt || "").trim()

    let imageDataURL: string | undefined
    if (img) {
      try {
        // 先在本站侧抓取并转为 data:URL，避免在 Runway 侧跨域/混合内容被拦截
        const res = await fetch(img)
        const blob = await res.blob()
        const reader = new FileReader()
        imageDataURL = await new Promise<string>((resolve, reject) => {
          reader.onload = () => resolve(String(reader.result || ""))
          reader.onerror = () => reject(new Error("readAsDataURL failed"))
          reader.readAsDataURL(blob)
        })
      } catch {
        // 失败则退化为原始 URL
        imageDataURL = undefined
      }
    }

    const payload = {
      mode: "image_to_video",
      imageUrl: img,
      imageDataURL, // 优先使用 data:URL
      videoPrompt: prompt,
    }

    try {
      if (prompt) navigator.clipboard?.writeText(prompt).catch(() => {})
    } catch {}

    // 首选：将数据保存到本地接口，Runway 通过 svFetch 拉取
    try {
      const resp = await fetch('/api/runway/prefill', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
      if (resp.ok) {
        const { fetchUrl } = await resp.json()
        const openUrl = `https://app.runwayml.com/?svFetch=${encodeURIComponent(fetchUrl)}`
        // 混合策略：同时写入 window.name，Runway 侧既可用 svFetch 拉取，也可用 window.name 兜底
        const w = window.open('about:blank', '_blank')
        if (w) {
          try { w.name = `SVPREFILL:${JSON.stringify(payload)}` } catch {}
          w.location.href = openUrl
        } else {
          window.open(openUrl, '_blank', 'noopener,noreferrer')
        }
        return
      }
    } catch {}

    // 回退：使用 window.name 传递，避免超长 URL 触发 CloudFront 494
    const w = window.open('about:blank', '_blank')
    if (w) {
      try { w.name = `SVPREFILL:${JSON.stringify(payload)}` } catch {}
      w.location.href = 'https://app.runwayml.com/'
    } else {
      const url = `https://app.runwayml.com/?svPrefill=1`
      window.open(url, '_blank')
    }
  }

  const disabled = !imageUrl || !videoPrompt || !videoPrompt.trim()

  return (
    <Button
      onClick={openRunway}
      disabled={disabled}
      size={size}
      variant="outline"
      className={className || "border-purple-600 text-purple-400 hover:bg-purple-600/10 bg-transparent text-xs px-2 py-1"}
      title={disabled ? "需要图片与视频提示词" : "打开 Runway 并自动预填（需安装用户脚本）"}
    >
      在 Runway 打开
    </Button>
  )
}

