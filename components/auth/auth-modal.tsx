"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Mail, Lock, User, Eye, EyeOff, Loader2, AlertCircle, Zap } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [activeTab, setActiveTab] = useState("signin")
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
    confirmPassword: "",
  })

  const { signIn, signUp, signInDemo } = useAuth()
  const { toast } = useToast()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleDemoLogin = async () => {
    setIsLoading(true)
    try {
      await signInDemo()
      toast({
        title: "Welcome to Demo! 🎉",
        description: "You're now using ScriptVivid AI in demo mode. All features are available!",
      })
      onClose()
      setFormData({ email: "", password: "", name: "", confirmPassword: "" })
    } catch (error: any) {
      console.error("Demo login error:", error)
      toast({
        title: "Demo Login Failed",
        description: "Something went wrong with demo mode. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Sign in attempt:", { email: formData.email, password: "***" })

    if (!formData.email || !formData.password) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      await signIn(formData.email, formData.password)
      toast({
        title: "Success! 🎉",
        description: "Welcome back! You've been signed in successfully.",
      })
      onClose()
      setFormData({ email: "", password: "", name: "", confirmPassword: "" })
    } catch (error: any) {
      console.error("Sign in error:", error)

      let errorMessage = "Sign in failed. Please try again."
      let suggestion = ""

      if (
        error.message?.includes("Invalid login credentials") ||
        error.message?.includes("Invalid email or password")
      ) {
        errorMessage = "Invalid email or password."
        suggestion = "Double-check your credentials or try the demo account instead."
      } else if (error.message?.includes("Email not confirmed")) {
        errorMessage = "Please verify your email address first."
        suggestion = "Check your email for a verification link."
      } else if (error.message?.includes("Too many requests")) {
        errorMessage = "Too many login attempts."
        suggestion = "Please wait a few minutes before trying again."
      } else {
        errorMessage = error.message || "An unexpected error occurred."
      }

      toast({
        title: "Sign In Failed",
        description: `${errorMessage} ${suggestion}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Sign up attempt:", { email: formData.email, name: formData.name })

    if (!formData.email || !formData.password || !formData.name) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive",
      })
      return
    }

    if (formData.password.length < 6) {
      toast({
        title: "Error",
        description: "Password must be at least 6 characters long",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      await signUp(formData.email, formData.password, formData.name)

      toast({
        title: "Account Created! 🎉",
        description: "Your account has been created successfully. Please check your email to verify your account.",
      })

      // Switch to sign in tab and keep the email
      setActiveTab("signin")
      setFormData({
        email: formData.email,
        password: "",
        name: "",
        confirmPassword: "",
      })
    } catch (error: any) {
      console.error("Sign up error:", error)

      let errorMessage = "Failed to create account. Please try again."

      if (error.message?.includes("User already registered")) {
        errorMessage = "An account with this email already exists."
        toast({
          title: "Account Exists",
          description: "This email is already registered. Please sign in instead.",
        })
        setActiveTab("signin")
        setFormData({
          email: formData.email,
          password: "",
          name: "",
          confirmPassword: "",
        })
        return
      } else if (error.message?.includes("Password should be at least")) {
        errorMessage = "Password is too weak. Please use a stronger password."
      } else if (error.message?.includes("email")) {
        errorMessage = error.message
      }

      toast({
        title: "Sign Up Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-white text-center">Welcome to ScriptVivid AI</DialogTitle>
          <DialogDescription className="text-gray-400 text-center">
            Get started with our AI-powered video creation platform
          </DialogDescription>
        </DialogHeader>

        {/* Demo Mode - Prominent */}
        <div className="mb-6">
          <Alert className="mb-4 border-green-500/50 bg-green-500/10">
            <Zap className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-200">
              <strong>Try Demo Mode:</strong> Instant access to all features without registration!
            </AlertDescription>
          </Alert>

          <Button
            onClick={handleDemoLogin}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 font-semibold text-lg py-3"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Starting Demo...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-5 w-5" />
                Start Demo Mode
              </>
            )}
          </Button>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-gray-900 px-2 text-gray-400">Or create an account</span>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gray-800">
            <TabsTrigger
              value="signin"
              className="text-gray-300 data-[state=active]:text-white data-[state=active]:bg-gray-700"
            >
              Sign In
            </TabsTrigger>
            <TabsTrigger
              value="signup"
              className="text-gray-300 data-[state=active]:text-white data-[state=active]:bg-gray-700"
            >
              Sign Up
            </TabsTrigger>
          </TabsList>

          <TabsContent value="signin">
            <Card className="bg-transparent border-gray-800">
              <CardHeader className="text-center">
                <CardTitle className="text-white">Sign In</CardTitle>
                <CardDescription className="text-gray-400">
                  Enter your credentials to access your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSignIn} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="signin-email" className="text-gray-300">
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signin-email"
                        name="email"
                        type="email"
                        placeholder="Enter your email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signin-password" className="text-gray-300">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signin-password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="pl-10 pr-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-500 hover:to-orange-600 font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing In...
                      </>
                    ) : (
                      "Sign In"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="signup">
            <Card className="bg-transparent border-gray-800">
              <CardHeader className="text-center">
                <CardTitle className="text-white">Create Account</CardTitle>
                <CardDescription className="text-gray-400">
                  Join ScriptVivid AI and start creating amazing videos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert className="mb-4 border-blue-500/50 bg-blue-500/10">
                  <AlertCircle className="h-4 w-4 text-blue-400" />
                  <AlertDescription className="text-blue-200">
                    <strong>Note:</strong> You'll need to verify your email address after registration.
                  </AlertDescription>
                </Alert>

                <form onSubmit={handleSignUp} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="signup-name" className="text-gray-300">
                      Full Name
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signup-name"
                        name="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-email" className="text-gray-300">
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signup-email"
                        name="email"
                        type="email"
                        placeholder="Enter your email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-password" className="text-gray-300">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signup-password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Create a password (min. 6 characters)"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="pl-10 pr-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                        minLength={6}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-confirm-password" className="text-gray-300">
                      Confirm Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="signup-confirm-password"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm your password"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className="pl-10 bg-gray-800 border-gray-700 text-white placeholder:text-gray-500"
                        disabled={isLoading}
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-500 hover:to-orange-600 font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      "Create Account"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="text-center text-sm text-gray-400 mt-4">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </div>
      </DialogContent>
    </Dialog>
  )
}
