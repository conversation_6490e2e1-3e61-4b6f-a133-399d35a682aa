"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Zap, User, Settings, LogOut, BookOpen } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

export function TopBar() {
  const { user, signOut } = useAuth()

  return (
    <div className="flex items-center justify-between p-4 bg-gray-900 border-b border-gray-800">
      <div className="flex-1" />

      <div className="flex items-center space-x-4">
        {/* Credits */}
        <div className="flex items-center space-x-2 text-yellow-400">
          <Zap className="w-4 h-4" />
          <span className="text-sm font-medium">0</span>
        </div>

        {/* Upgrade Button */}
        <Button
          size="sm"
          className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-500 hover:to-orange-600 font-medium"
        >
          Upgrade 升级
        </Button>

        {/* Documentation */}
        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <BookOpen className="w-4 h-4" />
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="w-8 h-8 rounded-full bg-orange-500 text-white font-medium">
              {user?.name?.[0] || user?.email?.[0] || "2h"}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
            <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
              <User className="w-4 h-4 mr-2" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem onClick={signOut} className="text-gray-300 hover:text-white hover:bg-gray-700">
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
